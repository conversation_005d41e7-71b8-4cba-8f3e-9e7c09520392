# GraphQL Server Example with NestJS (code-first)

This example shows how to implement an **GraphQL server (code-first) with TypeScript** with the following stack:

- [NestJS](https://docs.nestjs.com/graphql/quick-start): Web framework for building scalable server-side applications
- [**Prisma Client**](https://www.prisma.io/docs/concepts/components/prisma-client): Databases access (ORM)
- [**Prisma Migrate**](https://www.prisma.io/docs/concepts/components/prisma-migrate): Database migrations
- [**PostgresqlDB**](https://www.postgresql.org/)

The example was bootstrapped using the NestJS CLI command `nest new nest-graphql`.

## Getting started

### 1. Download example and navigate into the project directory

Download this example:

```
npx try-prisma@latest --template orm/nest-graphql
```

Then, navigate into the project directory:

```
cd nest-graphql
```

<details><summary><strong>Alternative:</strong> Clone the entire repo</summary>

Clone this repository:

```
<NAME_EMAIL>:prisma/prisma-examples.git --depth=1
```

Install npm dependencies:

```
cd prisma-examples/orm/nest-graphql
npm install
```

</details>

#### [Optional] Switch database to Prisma Postgres

This example uses a local SQLite database by default. If you want to use to [Prisma Postgres](https://prisma.io/postgres), follow these instructions (otherwise, skip to the next step):

1. Set up a new Prisma Postgres instance in the Prisma Data Platform [Console](https://console.prisma.io) and copy the database connection URL.
2. Update the `datasource` block to use `postgresql` as the `provider` and paste the database connection URL as the value for `url`:
    ```prisma
    datasource db {
      provider = "postgresql"
      url      = "prisma+postgres://accelerate.prisma-data.net/?api_key=ey...."
    }
    ```

   > **Note**: In production environments, we recommend that you set your connection URL via an [environment variable](https://www.prisma.io/docs/orm/more/development-environment/environment-variables/managing-env-files-and-setting-variables), e.g. using a `.env` file.
3. Install the Prisma Accelerate extension:
    ```
    npm install @prisma/extension-accelerate
    ```
4. Add the Accelerate extension to the `PrismaClient` instance:
    ```diff
    + import { withAccelerate } from "@prisma/extension-accelerate"

    + const prisma = new PrismaClient().$extends(withAccelerate())
    ```

That's it, your project is now configured to use Prisma Postgres!

### 2. Create and seed the database

Run the following command to create your database. This also creates the `User` and `Post` tables that are defined in [`prisma/schema.prisma`](./prisma/schema.prisma):

```
npx prisma migrate dev --name init
```

When `npx prisma migrate dev` is executed against a newly created database, seeding is also triggered. The seed file in [`prisma/seed.ts`](./prisma/seed.ts) will be executed and your database will be populated with the sample data.

**If you switched to Prisma Postgres in the previous step**, you need to trigger seeding manually (because Prisma Postgres already created an empty database instance for you, so seeding isn't triggered):

```
npx prisma db seed
```


### 3. Start the GraphQL server

Launch your GraphQL server with this command:

```
npm run dev
```

Navigate to [http://localhost:3000/graphql](http://localhost:3000/graphql) in your browser to explore the API of your GraphQL server in a [GraphQL Playground](https://github.com/prisma/graphql-playground).



## Using the GraphQL API

The schema that specifies the API operations of your GraphQL server is defined in [`./schema.graphql`](./schema.graphql). Below are a number of operations that you can send to the API using the GraphQL Playground.

Feel free to adjust any operation by adding or removing fields. The GraphQL Playground helps you with its auto-completion and query validation features.

### Retrieve all published posts and their authors

```graphql
query {
  feed {
    id
    title
    content
    published
    author {
      id
      name
      email
    }
  }
}
```

<details><summary><strong>See more API operations</strong></summary>

### Retrieve the drafts of a user

```graphql
{
  draftsByUser(
    userUniqueInput: {
      email: "<EMAIL>"
    }
  ) {
    id
    title
    content
    published
    author {
      id
      name
      email
    }
  }
}
```


### Create a new user

```graphql
mutation {
  signupUser(data: { name: "Sarah", email: "<EMAIL>" }) {
    id
  }
}
```

### Create a new draft

```graphql
mutation {
  createDraft(
    data: { title: "Join the Prisma Discord", content: "https://pris.ly/discord" }
    authorEmail: "<EMAIL>"
  ) {
    id
    viewCount
    published
    author {
      id
      name
    }
  }
}
```

### Publish/unpublish an existing post

```graphql
mutation {
  togglePublishPost(id: __POST_ID__) {
    id
    published
  }
}
```

Note that you need to replace the `__POST_ID__` placeholder with an actual `id` from a `Post` record in the database, e.g.`5`:

```graphql
mutation {
  togglePublishPost(id: 5) {
    id
    published
  }
}
```

### Increment the view count of a post

```graphql
mutation {
  incrementPostViewCount(id: __POST_ID__) {
    id
    viewCount
  }
}
```

Note that you need to replace the `__POST_ID__` placeholder with an actual `id` from a `Post` record in the database, e.g.`5`:

```graphql
mutation {
  incrementPostViewCount(id: 5) {
    id
    viewCount
  }
}
```

### Search for posts that contain a specific string in their title or content

```graphql
{
  feed(
    searchString: "prisma"
  ) {
    id
    title
    content
    published
  }
}
```

### Paginate and order the returned posts

```graphql
{
  feed(
    skip: 2
    take: 2
    orderBy: { updatedAt: desc }
  ) {
    id
    updatedAt
    title
    content
    published
  }
}
```

### Retrieve a single post

```graphql
{
  postById(id: __POST_ID__ ) {
    id
    title
    content
    published
  }
}
```

Note that you need to replace the `__POST_ID__` placeholder with an actual `id` from a `Post` record in the database, e.g.`5`:

```graphql
{
  postById(id: 5 ) {
    id
    title
    content
    published
  }
}
```

### Delete a post

```graphql
mutation {
  deletePost(id: __POST_ID__) {
    id
  }
}
```

Note that you need to replace the `__POST_ID__` placeholder with an actual `id` from a `Post` record in the database, e.g.`5`:

```graphql
mutation {
  deletePost(id: 5) {
    id
  }
}
```

</details>


## Evolving the app

Evolving the application typically requires two steps:

1. Migrate your database using Prisma Migrate
1. Update your application code

For the following example scenario, assume you want to add a "profile" feature to the app where users can create a profile and write a short bio about themselves.

### 1. Migrate your database using Prisma Migrate

The first step is to add a new table, e.g. called `Profile`, to the database. You can do this by adding a new model to your [Prisma schema file](./prisma/schema.prisma) file and then running a migration afterwards:

```diff
// schema.prisma

model Post {
  id        Int     @default(autoincrement()) @id
  title     String
  content   String?
  published Boolean @default(false)
  author    User?   @relation(fields: [authorId], references: [id])
  authorId  Int
}

model User {
  id      Int      @default(autoincrement()) @id
  name    String?
  email   String   @unique
  posts   Post[]
+ profile Profile?
}

+model Profile {
+  id     Int     @default(autoincrement()) @id
+  bio    String?
+  userId Int     @unique
+  user   User    @relation(fields: [userId], references: [id])
+}
```

Once you've updated your data model, you can execute the changes against your database with the following command:

```
npx prisma migrate dev
```

### 2. Update your application code

You can now use your `PrismaClient` instance to perform operations against the new `Profile` table. Here are some examples:

#### Create a new profile for an existing user

```ts
const profile = await prisma.profile.create({
  data: {
    bio: "Hello World",
    user: {
      connect: { email: "<EMAIL>" },
    },
  },
});
```

#### Create a new user with a new profile

```ts
const user = await prisma.user.create({
  data: {
    email: "<EMAIL>",
    name: "John",
    profile: {
      create: {
        bio: "Hello World",
      },
    },
  },
});
```

#### Update the profile of an existing user

```ts
const userWithUpdatedProfile = await prisma.user.update({
  where: { email: "<EMAIL>" },
  data: {
    profile: {
      update: {
        bio: "Hello Friends",
      },
    },
  },
});
```

## Next steps

- Check out the [Prisma docs](https://www.prisma.io/docs)
- Share your feedback on the [Prisma Discord](https://pris.ly/discord/)
- Create issues and ask questions on [GitHub](https://github.com/prisma/prisma/)


## Pension Data Calculation
1. **Base Pension Calculation**
   - Calculated from left to Right 
     - **Gross fulltime monthly salary** = gross parttime monthly salary / parttime percentage
     - **Gross fulltime annual salary** = annual multiplier x gross parttime monthly salary
     - **Pension base** = gross fulltime annual salary - offset
     - NB:: _Annual multiplier and offset are pension parameters_

2. **Pensions end of previous calendar year**
     -

# Pension Calculation Formulas

This document explains the key pension calculation formulas extracted from the pension administration Excel file.

## OP-TE (Retirement Pension - Current Status)
**Formula:** `IF(AC21="","",F21+IF(Y21="",0,Y21))`

* If the ID field (AC21) is empty, return an empty string
* Otherwise, add the base pension value (F21) to the additional pension accrual (Y21)
* Located in cell AH21, referencing F21 (base value) and Y21 (calculated accrual)

## WP-TE (Partner's Pension - Current Status)

**Formula:** `IF(AC21="","",G21+IF(Z21="",0,Z21))`

* If the ID field (AC21) is empty, return an empty string
* Otherwise, add the base partner pension value (G21) to the additional partner pension accrual (Z21)
* Located in cell AI21, referencing G21 (base value) and Z21 (calculated accrual)

## ONP-TE (Child's Pension - Current Status)

**Formula:** `H21`

* Simply returns the base child pension value from H21
* Located in cell AJ21 with a direct reference to H21 (no calculations)

## AOP-TE (Disability Pension - Current Status)

There is no explicit formula with AOP-TE in the sheet. The AOP values are in columns I and AK. In both cases, they appear to be direct values rather than calculated values.

## Supporting Formulas and Constants

### Y21 Formula (additional pension accrual)
**Formula:** `IF(OR($A21=10,$A21=11),$A$4*M21*T21*X21,"")`

* If the person has status 10 or 11, calculate:
* Accrual rate (A4 = 0.02) × Part-time factor (M21 = 1) × Service period (T21 = 1) × Pension base (X21 = 43224)
* Otherwise, return an empty string

### Z21 Formula (additional partner pension accrual)
**Formula:** `IF(OR($A21=10,$A21=11),$A$7*Y21,"")`

* If the person has status 10 or 11, calculate:
* Partner pension rate (A7 = 0.7) × Additional pension accrual (Y21)
* Otherwise, return an empty string

### Constants used:
* A4 = 0.02 (2% accrual rate)
* A5 = 17616 (Offset value 1)
* A6 = 11756 (Offset value 2)
* A7 = 0.7 (70% partner pension rate)



   _-NB: Users should only be able to add +1 of the current Year hence in 2025 they can add only up to 2026_

## CertifiedData Model Migration

We've updated the CertifiedData model to use relational structures instead of JSON fields.
### Changes Made:

- Replaced JSON fields with proper relational models:
  - `pensionInfo` → `CertifiedPensionInfo`
  - `employmentInfo` → `CertifiedEmploymentInfo`
  - `personalInfo` → `CertifiedPersonalInfo`
  - `indexationStartOfYear` → `CertifiedIndexationStartOfYear`
  - `pensionCorrectionsStartOfYear` → `CertifiedPensionCorrections`
  - `voluntaryAdditionalContributions` → `CertifiedVoluntaryContributions`
  - `pensionParameters` → `CertifiedPensionParameters`

### Benefits:

- Type safety: Each field now has proper typing instead of being stored as generic JSON
- Query performance: Better indexing and more efficient queries
- Data integrity: Relationships are now properly enforced by the database
- Maintainability: Easier to understand and work with the data model