#!/bin/bash -e

# Function to show git history
show_git_history() {
    git for-each-ref --sort=committerdate refs/heads/ --format='%(HEAD) %(align:35)%(color:yellow)%(refname:short)%(color:reset)%(end) - %(color:red)%(objectname:short)%(color:reset) - %(align:40)%(contents:subject)%(end) - %(authorname) (%(color:green)%(committerdate:relative)%(color:reset))'
}

# Function to create new branch with commit
create_new_branch() {
    local commit_message="$1"
    branchName=$(git rev-parse --abbrev-ref HEAD) #Get Current Branch Name
    git add .
    git commit -m "$commit_message"
    branchNum=$(cut -d'-' -f1 <<<"$branchName")
    branchAbbr=$(cut -d'-' -f2 <<<"$branchName")
    newBranchNum=$((branchNum+1))
    git checkout -b "$newBranchNum-$branchAbbr-$(date +%Y.%b.%d.%A_%H-%M-%S)"
}

# Check if argument is provided
if [ $# -eq 0 ]; then
    echo "Usage: ./git.sh <commit message> or ./git.sh -history"
    exit 1
fi

# Check first argument
case "$1" in
    -history)
        show_git_history
        ;;
    *)
        create_new_branch "$1"
        ;;
esac
