version: '3.1'

services:
  postgres:
    networks:
      default:
        aliases:
          - postgres
    image: postgres:10
    container_name: pensionadmin_pdb
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: aximilli1212
      POSTGRES_PASSWORD: aximilli1212
      POSTGRES_DB: pensionadmin
    volumes:
      - postgres_data:/var/lib/postgresql/data

  postgis:
    networks:
      default:
        aliases:
          - postgis
    image: postgis/postgis
    container_name: pensionadmin_postgis
    ports:
      - "5433:5432"
    environment:
      POSTGRES_USER: aximilli1212
      POSTGRES_PASSWORD: aximilli1212
      POSTGRES_DB: pensionadmin
    volumes:
      - postgis_data:/var/lib/postgresql/data

  app:
    env_file:
      - .env
    build:
      context: .
      dockerfile: Dockerfile
    container_name: pensionadmin_backend
    environment:
      - PORT=${PORT}
    ports:
      - '3000:3000'
    depends_on:
      - postgres
    volumes:
      - ./src:/app/src

  pgadmin:
    image: dpage/pgadmin4
    restart: always
    container_name: pensionadmin_pgadmin
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=pgadmin4
    ports:
      - '5050:80'
    depends_on:
        - postgres

  redis:
    image: redis:6.2-alpine
    container_name: pensionadmin_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --save 20 1 --loglevel warning

volumes:
  postgres_data:
  postgis_data:
  redis_data:

