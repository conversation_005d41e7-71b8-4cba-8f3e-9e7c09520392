import { ObjectType, Field, ID, Int } from '@nestjs/graphql'
import { PartnerInfo } from '../../partner-info/entities/partner-info.entity'
import { Child } from '../../child/entities/child.entity'
import { Address } from '../../address/entities/address.entity'

@ObjectType()
export class PersonalInfo {
    @Field(() => ID)
    id: string

    @Field()
    participantId: string

    @Field()
    firstName: string

    @Field()
    lastName: string

    @Field({ nullable: true })
    email?: string

    @Field({ nullable: true })
    phone?: string

    @Field(() => [String], { nullable: true })
    pendingChanges?: string[]

    @Field({ nullable: true })
    maritalStatus?: string

    @Field(() => Int, { nullable: true })
    birthDay?: number

    @Field(() => Int, { nullable: true })
    birthMonth?: number

    @Field(() => Int, { nullable: true })
    birthYear?: number

    @Field(() => [PartnerInfo], { nullable: true })
    partnerInfo?: PartnerInfo[]

    @Field(() => [Child], { nullable: true })
    children?: Child[]

    @Field(() => Address, { nullable: true })
    address?: Address
}
