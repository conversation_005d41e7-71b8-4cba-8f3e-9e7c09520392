import { Resolver, Query, Mutation, Args, ID } from '@nestjs/graphql'
import { PersonalInfoService } from './personal-info.service'
import { PersonalInfo } from './entities/personal-info.entity'
import { CreatePersonalInfoInput } from './dto/create-personal-info.input'
import { UpdatePersonalInfoInput } from './dto/update-personal-info.input'

@Resolver(() => PersonalInfo)
export class PersonalInfoResolver {
    constructor(private readonly personalInfoService: PersonalInfoService) {}

    @Query(() => [PersonalInfo], { name: 'personalInfos' })
    findAll() {
        return this.personalInfoService.findAll()
    }

    @Query(() => PersonalInfo, { name: 'personalInfo' })
    findOne(@Args('id', { type: () => ID }) id: string) {
        return this.personalInfoService.findOne(id)
    }

    @Mutation(() => PersonalInfo)
    removePersonalInfo(@Args('id', { type: () => ID }) id: string) {
        return this.personalInfoService.remove(id)
    }
}
