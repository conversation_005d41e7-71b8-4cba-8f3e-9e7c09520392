import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql'
import { UseGuards } from '@nestjs/common'
import { CertifiedPensionParametersService } from './certified-pension-parameters.service'
import { CertifiedPensionParameters } from '../certified-data/entities/certified-pension-parameters.entity'
import { CreateCertifiedPensionParametersInput } from '../certified-data/dto/create-certified-pension-parameters.input'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'

@Resolver(() => CertifiedPensionParameters)
export class CertifiedPensionParametersResolver {
    constructor(
        private readonly certifiedParametersService: CertifiedPensionParametersService
    ) {}

    @UseGuards(GraphqlAuthGuard)
    @Query(() => CertifiedPensionParameters)
    async certifiedPensionParameters(@Args('id') id: string) {
        return this.certifiedParametersService.findOne(id)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => CertifiedPensionParameters)
    async certifiedParametersByCertifiedDataId(
        @Args('certifiedDataId') certifiedDataId: string
    ) {
        return this.certifiedParametersService.findByCertifiedDataId(
            certifiedDataId
        )
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => CertifiedPensionParameters)
    async createCertifiedPensionParameters(
        @Args('createCertifiedParametersInput')
        createCertifiedParametersInput: CreateCertifiedPensionParametersInput,
        @Args('certifiedDataId') certifiedDataId: string,
        @Context() context: any
    ) {
        return this.certifiedParametersService.create(
            createCertifiedParametersInput,
            certifiedDataId
        )
    }
}
