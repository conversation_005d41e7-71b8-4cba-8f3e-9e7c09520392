import { Module } from '@nestjs/common'
import { CertifiedPensionParametersService } from './certified-pension-parameters.service'
import { CertifiedPensionParametersResolver } from './certified-pension-parameters.resolver'
import { AuthModule } from '../auth/auth.module'

@Module({
    imports: [AuthModule],
    providers: [
        CertifiedPensionParametersResolver,
        CertifiedPensionParametersService,
    ],
    exports: [CertifiedPensionParametersService],
})
export class CertifiedPensionParametersModule {}
