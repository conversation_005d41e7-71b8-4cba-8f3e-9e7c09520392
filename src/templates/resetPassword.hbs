<!doctype html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>{{ title }}</title>
    <style>
        /* GLOBAL RESETS */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        img {
            border: none;
            -ms-interpolation-mode: bicubic;
            max-width: 100%;
        }

        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            -webkit-font-smoothing: antialiased;
            font-size: 16px;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            -ms-text-size-adjust: 100%;
            -webkit-text-size-adjust: 100%;
            color: #333333;
        }

        table {
            border-collapse: separate;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
            width: 100%;
        }

        table td {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 16px;
            vertical-align: top;
        }

        /* BODY & CONTAINER */
        .body {
            background-color: #f8f9fa;
            width: 100%;
        }

        .container {
            display: block;
            margin: 0 auto !important;
            max-width: 600px;
            padding: 20px;
            width: 600px;
        }

        .content {
            box-sizing: border-box;
            display: block;
            margin: 0 auto;
            max-width: 600px;
            padding: 0;
        }

        /* HEADER, FOOTER, MAIN */
        .header {
            padding: 20px 0;
            text-align: center;
        }

        .header img {
            height: 60px;
            width: auto;
        }

        .main {
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
            width: 100%;
            overflow: hidden;
        }

        .wrapper {
            box-sizing: border-box;
            padding: 30px;
        }

        .content-block {
            padding-bottom: 10px;
            padding-top: 10px;
        }

        .footer {
            clear: both;
            margin-top: 20px;
            text-align: center;
            width: 100%;
            padding: 20px 0;
        }

        .footer td,
        .footer p,
        .footer span,
        .footer a {
            color: #6c757d;
            font-size: 14px;
            text-align: center;
        }

        /* TYPOGRAPHY */
        h1,
        h2,
        h3,
        h4 {
            color: #0072C3;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-weight: 600;
            line-height: 1.4;
            margin: 0;
            margin-bottom: 24px;
        }

        h1 {
            font-size: 28px;
            text-align: center;
        }

        p,
        ul,
        ol {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 16px;
            font-weight: normal;
            margin: 0;
            margin-bottom: 20px;
        }

        p li,
        ul li,
        ol li {
            list-style-position: inside;
            margin-left: 5px;
        }

        a {
            color: #0072C3;
            text-decoration: underline;
        }

        /* BUTTONS */
        .btn {
            box-sizing: border-box;
            width: 100%;
        }

        .btn > tbody > tr > td {
            padding-bottom: 20px;
        }

        .btn table {
            width: auto;
        }

        .btn table td {
            background-color: #ffffff;
            border-radius: 5px;
            text-align: center;
        }

        .btn a {
            background-color: #ffffff;
            border: solid 1px #0072C3;
            border-radius: 6px;
            box-sizing: border-box;
            color: #0072C3;
            cursor: pointer;
            display: inline-block;
            font-size: 16px;
            font-weight: 600;
            margin: 0;
            padding: 12px 30px;
            text-decoration: none;
            text-transform: capitalize;
            transition: all 0.3s ease;
        }

        .btn-primary table td {
            background-color: #0072C3;
        }

        .btn-primary a {
            background-color: #0072C3;
            border-color: #0072C3;
            color: #ffffff;
        }

        .btn-primary a:hover {
            background-color: #005a9e;
            border-color: #005a9e;
        }

        .preheader {
            color: transparent;
            display: none;
            height: 0;
            max-height: 0;
            max-width: 0;
            opacity: 0;
            overflow: hidden;
            mso-hide: all;
            visibility: hidden;
            width: 0;
        }

        .greeting {
            font-weight: 600;
            font-size: 18px;
            margin-bottom: 15px;
        }

        .company-info {
            border-top: 1px solid #e9ecef;
            padding-top: 20px;
            margin-top: 30px;
        }

        hr {
            border: 0;
            border-bottom: 1px solid #e9ecef;
            margin: 30px 0;
        }

        /* RESPONSIVE */
        @media only screen and (max-width: 620px) {
            table[class=body] h1 {
                font-size: 24px !important;
                margin-bottom: 20px !important;
            }

            table[class=body] p,
            table[class=body] ul,
            table[class=body] ol,
            table[class=body] td,
            table[class=body] span,
            table[class=body] a {
                font-size: 16px !important;
            }

            table[class=body] .wrapper,
            table[class=body] .article {
                padding: 20px !important;
            }

            table[class=body] .content {
                padding: 0 !important;
            }

            table[class=body] .container {
                padding: 0 !important;
                width: 100% !important;
            }

            table[class=body] .main {
                border-left-width: 0 !important;
                border-radius: 0 !important;
                border-right-width: 0 !important;
            }

            table[class=body] .btn table {
                width: 100% !important;
            }

            table[class=body] .btn a {
                width: 100% !important;
            }
        }

        @media all {
            .apple-link a {
                color: inherit !important;
                font-family: inherit !important;
                font-size: inherit !important;
                font-weight: inherit !important;
                line-height: inherit !important;
                text-decoration: none !important;
            }

            #MessageViewBody a {
                color: inherit;
                text-decoration: none;
                font-size: inherit;
                font-family: inherit;
                font-weight: inherit;
                line-height: inherit;
            }
        }
    </style>
</head>
<body class="">
<span class="preheader">Please reset your password.</span>
<table role="presentation" border="0" cellpadding="0" cellspacing="0" class="body">
    <tr>
        <td>&nbsp;</td>
        <td class="container">
            <div class="content">
                <!-- LOGO HEADER -->
                <div class="header">
                    <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
                        <tr>
                            <td class="align-center" width="100%">
                                <div style="font-size: 24px; font-weight: bold; color: #0072C3;">Pension Admin</div>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- WHITE CONTAINER -->
                <table role="presentation" class="main">
                    <!-- MAIN CONTENT AREA -->
                    <tr>
                        <td class="wrapper">
                            <table role="presentation" border="0" cellpadding="0" cellspacing="0">
                                <tr>
                                    <td>
                                        <h1>{{ title }}</h1>
                                        <p class="greeting">Hello {{nameUser}},</p>
                                        <p>{{description}}</p>
                                        <table role="presentation" border="0" cellpadding="0" cellspacing="0" class="btn btn-primary">
                                            <tbody>
                                            <tr>
                                                <td align="center">
                                                    <table role="presentation" border="0" cellpadding="0" cellspacing="0">
                                                        <tbody>
                                                        <tr>
                                                            <td> <a href={{resetLink}} target="_blank">Set Password</a> </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>

                                        <p style="margin-top: 30px;">If the button above doesn't work, you can copy and paste the following link into your browser:</p>
                                        <p style="font-size: 14px; word-break: break-all; color: #6c757d;">{{resetLink}}</p>

                                        <div class="company-info">
                                            <p>If you have any questions, please don't hesitate to contact our support team.</p>
                                            <p>{{footer}}</p>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>

                <!-- FOOTER -->
                <div class="footer">
                    <table role="presentation" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td class="content-block">
                                <span class="apple-link">Best regards, Your Pension Admin Team</span>
                                <br>
                                <br>
                                <span style="color: #6c757d; font-size: 12px;">© 2025 Pension Admin. All rights reserved.</span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </td>
        <td>&nbsp;</td>
    </tr>
</table>
</body>
</html>