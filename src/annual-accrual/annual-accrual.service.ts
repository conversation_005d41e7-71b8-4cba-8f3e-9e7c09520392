import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateAnnualAccrualInput } from './dto/create-annual-accrual.input'
import { UpdateAnnualAccrualInput } from './dto/update-annual-accrual.input'
import { AnnualAccrual } from './entities/annual-accrual.entity'

@Injectable()
export class AnnualAccrualService {
    constructor(private readonly prisma: PrismaService) {}

    async create(createAnnualAccrualInput: CreateAnnualAccrualInput) {
        const annualAccrual = await this.prisma.annualAccrual.create({
            data: {
                ...createAnnualAccrualInput,
            },
            include: {
                pensionData: true,
            },
        })

        if (!annualAccrual) {
            throw new NotFoundException('AnnualAccrual not created')
        }

        return annualAccrual
    }

    async findAll() {
        return this.prisma.annualAccrual.findMany({
            include: {
                pensionData: true,
            },
        })
    }

    async findOne(id: string) {
        const annualAccrual = await this.prisma.annualAccrual.findUnique({
            where: { id },
            include: {
                pensionData: true,
            },
        })

        if (!annualAccrual) {
            throw new NotFoundException('AnnualAccrual not found')
        }

        return annualAccrual
    }

    async update(updateAnnualAccrualInput: UpdateAnnualAccrualInput) {
        const annualAccrual = await this.prisma.annualAccrual.update({
            where: { id: updateAnnualAccrualInput.id },
            data: {
                ...updateAnnualAccrualInput,
            },
            include: {
                pensionData: true,
            },
        })

        if (!annualAccrual) {
            throw new NotFoundException('AnnualAccrual not updated')
        }

        return annualAccrual
    }

    async delete(id: string) {
        const annualAccrual = await this.prisma.annualAccrual.delete({
            where: { id },
        })

        if (!annualAccrual) {
            throw new NotFoundException('AnnualAccrual not deleted')
        }

        return annualAccrual
    }
}
