import { Resolver, Query, Mutation, Args } from '@nestjs/graphql'
import { AnnualAccrualService } from './annual-accrual.service'
import { AnnualAccrual } from './entities/annual-accrual.entity'
import { CreateAnnualAccrualInput } from './dto/create-annual-accrual.input'
import { UpdateAnnualAccrualInput } from './dto/update-annual-accrual.input'

@Resolver(() => AnnualAccrual)
export class AnnualAccrualResolver {
    constructor(private readonly annualAccrualService: AnnualAccrualService) {}

    @Mutation(() => AnnualAccrual)
    async createAnnualAccrual(
        @Args('createAnnualAccrualInput')
        createAnnualAccrualInput: CreateAnnualAccrualInput
    ) {
        return this.annualAccrualService.create(createAnnualAccrualInput)
    }

    @Query(() => [AnnualAccrual], { name: 'annualAccruals' })
    async findAll() {
        return this.annualAccrualService.findAll()
    }

    @Query(() => AnnualAccrual, { name: 'annualAccrual' })
    async findOne(@Args('id') id: string) {
        return this.annualAccrualService.findOne(id)
    }

    @Mutation(() => AnnualAccrual)
    async updateAnnualAccrual(
        @Args('updateAnnualAccrualInput')
        updateAnnualAccrualInput: UpdateAnnualAccrualInput
    ) {
        return this.annualAccrualService.update(updateAnnualAccrualInput)
    }

    @Mutation(() => AnnualAccrual)
    async deleteAnnualAccrual(@Args('id') id: string) {
        return this.annualAccrualService.delete(id)
    }
}
