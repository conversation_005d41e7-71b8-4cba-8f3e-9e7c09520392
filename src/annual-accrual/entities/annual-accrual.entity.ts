import { ObjectType, Field } from '@nestjs/graphql'
import { PensionData } from '../../pension-data/entities/pension-data.entity'

@ObjectType()
export class AnnualAccrual {
    @Field()
    id: string

    @Field(() => PensionData)
    pensionData: PensionData

    @Field({ nullable: true })
    employeeContributions?: number

    @Field({ nullable: true })
    employerContributions?: number

    @Field({ nullable: true })
    franchise?: number

    @Field({ nullable: true })
    monthlyBenefit?: number
}
