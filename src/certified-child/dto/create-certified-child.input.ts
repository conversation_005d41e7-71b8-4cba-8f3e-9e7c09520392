import { InputType, Field } from '@nestjs/graphql'
import { IsOptional, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { CreateCertificationRejectReasonInput } from '../../certification-reject-reason/dto/create-certification-reject-reason.input'

@InputType()
export class CreateCertifiedChildInput {
    @Field({ nullable: true })
    @IsOptional()
    firstName?: string

    @Field({ nullable: true })
    @IsOptional()
    lastName?: string

    @Field({ nullable: true })
    @IsOptional()
    dateOfBirth?: Date

    @Field({ nullable: true })
    @IsOptional()
    isOrphan?: boolean

    @Field({ nullable: true })
    @IsOptional()
    isStudying?: boolean

    @Field(() => [CreateCertificationRejectReasonInput], { nullable: true })
    @ValidateNested({ each: true })
    @Type(() => CreateCertificationRejectReasonInput)
    @IsOptional()
    certificationRejectReason?: CreateCertificationRejectReasonInput[]
}
