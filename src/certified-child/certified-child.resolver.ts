import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql'
import { CertifiedChildService } from './certified-child.service'
import { CreateCertifiedChildInput } from './dto/create-certified-child.input'
import { UpdateCertifiedChildInput } from './dto/update-certified-child.input'
import { CertifiedChild } from '../certified-data/entities'

@Resolver(() => CertifiedChild)
export class CertifiedChildResolver {
    constructor(
        private readonly certifiedChildService: CertifiedChildService
    ) {}

    @Mutation(() => CertifiedChild)
    updateCertifiedChild(
        @Args('updateCertifiedChildInput')
        updateCertifiedChildInput: UpdateCertifiedChildInput
    ) {
        return this.certifiedChildService.update(
            updateCertifiedChildInput.id,
            updateCertifiedChildInput
        )
    }
}
