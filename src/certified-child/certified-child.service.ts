import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { UpdateCertifiedChildInput } from './dto/update-certified-child.input'

@Injectable()
export class CertifiedChildService {
    constructor(private readonly prisma: PrismaService) {}

    async findOne(id: string) {
        const certifiedChild = await this.prisma.certifiedChild.findUnique({
            where: { id },
            include: {
                certifiedData: true,
                certificationRejectReason: true,
            },
        })

        if (!certifiedChild) {
            throw new NotFoundException(
                `CertifiedChild with id ${id} not found`
            )
        }

        return certifiedChild
    }

    async update(
        id: string,
        updateCertifiedChildInput: UpdateCertifiedChildInput
    ) {
        const { certificationRejectReason, ...childData } =
            updateCertifiedChildInput

        return this.prisma.certifiedChild.update({
            where: { id },
            data: childData,
            include: {
                certifiedData: true,
                certificationRejectReason: true,
            },
        })
    }

    async updatePendingChanges(id: string, paths: string[]) {
        const certifiedChild = await this.prisma.certifiedChild.findUnique({
            where: { id },
        })

        if (!certifiedChild) {
            throw new NotFoundException(
                `CertifiedChild with ID ${id} not found`
            )
        }

        const currentChanges = certifiedChild.pendingChanges || []
        const uniqueChanges = [...new Set([...currentChanges, ...paths])]

        return this.prisma.certifiedChild.update({
            where: { id },
            data: {
                pendingChanges: uniqueChanges,
            },
        })
    }

    async clearPendingChanges(id: string, paths: string[]) {
        const certifiedChild = await this.prisma.certifiedChild.findUnique({
            where: { id },
        })

        if (!certifiedChild) {
            throw new NotFoundException(
                `CertifiedChild with ID ${id} not found`
            )
        }

        const currentChanges = certifiedChild.pendingChanges || []
        const updatedChanges = currentChanges.filter(
            (path) => !paths.includes(path)
        )

        return this.prisma.certifiedChild.update({
            where: { id },
            data: {
                pendingChanges: updatedChanges,
            },
        })
    }

    async changeUpdate({
        entityId,
        path,
        newValue,
    }: {
        entityId: string
        path: string
        newValue: any
    }) {
        const certifiedChild = await this.prisma.certifiedChild.findUnique({
            where: { id: entityId },
        })

        if (!certifiedChild) {
            throw new NotFoundException(
                `CertifiedChild with ID ${entityId} not found`
            )
        }

        return this.prisma.certifiedChild.update({
            where: { id: entityId },
            data: {
                [path]: newValue,
            },
            include: {
                certifiedData: true,
                certificationRejectReason: true,
            },
        })
    }

    async updateFieldByParticipantId({
        participantId,
        path,
        newValue,
    }: {
        participantId: string
        path: string
        newValue: any
    }) {
        // Find the child by participant ID (assuming there's a connection)
        const child = await this.prisma.child.findFirst({
            where: {
                personalInfo: {
                    participant: {
                        id: participantId,
                    },
                },
            },
        })

        if (!child) {
            throw new NotFoundException(
                `Child with participant ID ${participantId} not found`
            )
        }

        return this.prisma.child.update({
            where: { id: child.id },
            data: {
                [path]: newValue,
            },
        })
    }
}
