import { ObjectType, Field, ID, GraphQLISODateTime, registerEnumType } from '@nestjs/graphql'
import { CertificationRejectReasonStatus } from '.prisma/client'

// Register the enum for GraphQL
registerEnumType(CertificationRejectReasonStatus, {
    name: 'CertificationRejectReasonStatus',
    description: 'The status of a certification reject reason',
})

@ObjectType()
export class CertificationRejectReason {
    @Field(() => ID)
    id: string

    @Field()
    field: string

    @Field()
    reason: string

    @Field(() => CertificationRejectReasonStatus)
    status: CertificationRejectReasonStatus

    @Field(() => GraphQLISODateTime)
    createdAt: Date

    @Field(() => GraphQLISODateTime)
    updatedAt: Date

    @Field({ nullable: true })
    certifiedDataId?: string

    @Field({ nullable: true })
    certifiedPensionInfoId?: string

    @Field({ nullable: true })
    certifiedSalaryEntryId?: string

    @Field({ nullable: true })
    certifiedEmploymentInfoId?: string

    @Field({ nullable: true })
    certifiedPersonalInfoId?: string

    @Field({ nullable: true })
    certifiedIndexationStartOfYearId?: string

    @Field({ nullable: true })
    certifiedPensionCorrectionsId?: string

    @Field({ nullable: true })
    certifiedVoluntaryContributionsId?: string

    @Field({ nullable: true })
    certifiedPensionParametersId?: string
} 