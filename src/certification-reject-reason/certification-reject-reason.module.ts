import { Module } from '@nestjs/common'
import { CertificationRejectReasonService } from './certification-reject-reason.service'
import { CertificationRejectReasonResolver } from './certification-reject-reason.resolver'

@Module({
    providers: [CertificationRejectReasonResolver, CertificationRejectReasonService],
    exports: [CertificationRejectReasonService],
})
export class CertificationRejectReasonModule { } 