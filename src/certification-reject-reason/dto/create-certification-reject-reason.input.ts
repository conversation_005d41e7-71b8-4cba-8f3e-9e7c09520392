import { InputType, Field } from '@nestjs/graphql'
import { IsString, IsOptional, IsUUID, IsEnum } from 'class-validator'
import { CertificationRejectReasonStatus } from '@prisma/client'

@InputType()
export class CreateCertificationRejectReasonInput {
    @Field()
    @IsString()
    field: string

    @Field()
    @IsString()
    reason: string

    @Field(() => CertificationRejectReasonStatus, { nullable: true, defaultValue: CertificationRejectReasonStatus.VALID })
    @IsEnum(CertificationRejectReasonStatus)
    @IsOptional()
    status?: CertificationRejectReasonStatus

    @Field({ nullable: true })
    @IsUUID('4')
    @IsOptional()
    certifiedDataId?: string

    @Field({ nullable: true })
    @IsUUID('4')
    @IsOptional()
    certifiedPensionInfoId?: string

    @Field({ nullable: true })
    @IsUUID('4')
    @IsOptional()
    certifiedSalaryEntryId?: string

    @Field({ nullable: true })
    @IsUUID('4')
    @IsOptional()
    certifiedEmploymentInfoId?: string

    @Field({ nullable: true })
    @IsUUID('4')
    @IsOptional()
    certifiedPersonalInfoId?: string

    @Field({ nullable: true })
    @IsUUID('4')
    @IsOptional()
    certifiedIndexationStartOfYearId?: string

    @Field({ nullable: true })
    @IsUUID('4')
    @IsOptional()
    certifiedPensionCorrectionsId?: string

    @Field({ nullable: true })
    @IsUUID('4')
    @IsOptional()
    certifiedVoluntaryContributionsId?: string

    @Field({ nullable: true })
    @IsUUID('4')
    @IsOptional()
    certifiedPensionParametersId?: string
} 