import { Module } from '@nestjs/common'
import { CertifiedVoluntaryContributionsService } from './certified-voluntary-contributions.service'
import { CertifiedVoluntaryContributionsResolver } from './certified-voluntary-contributions.resolver'
import { AuthModule } from '../auth/auth.module'

@Module({
    imports: [AuthModule],
    providers: [
        CertifiedVoluntaryContributionsResolver,
        CertifiedVoluntaryContributionsService,
    ],
    exports: [CertifiedVoluntaryContributionsService],
})
export class CertifiedVoluntaryContributionsModule {}
