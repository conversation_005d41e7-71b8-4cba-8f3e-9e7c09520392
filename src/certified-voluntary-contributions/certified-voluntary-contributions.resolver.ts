import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql'
import { UseGuards } from '@nestjs/common'
import { CertifiedVoluntaryContributionsService } from './certified-voluntary-contributions.service'
import { CertifiedVoluntaryContributions } from '../certified-data/entities/certified-voluntary-contributions.entity'
import { CreateCertifiedVoluntaryContributionsInput } from '../certified-data/dto/create-certified-voluntary-contributions.input'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'

@Resolver(() => CertifiedVoluntaryContributions)
export class CertifiedVoluntaryContributionsResolver {
    constructor(
        private readonly certifiedContributionsService: CertifiedVoluntaryContributionsService
    ) {}

    @UseGuards(GraphqlAuthGuard)
    @Query(() => CertifiedVoluntaryContributions)
    async certifiedVoluntaryContributions(@Args('id') id: string) {
        return this.certifiedContributionsService.findOne(id)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => CertifiedVoluntaryContributions)
    async certifiedContributionsByCertifiedDataId(
        @Args('certifiedDataId') certifiedDataId: string
    ) {
        return this.certifiedContributionsService.findByCertifiedDataId(
            certifiedDataId
        )
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => CertifiedVoluntaryContributions)
    async createCertifiedVoluntaryContributions(
        @Args('createCertifiedContributionsInput')
        createCertifiedContributionsInput: CreateCertifiedVoluntaryContributionsInput,
        @Args('certifiedDataId') certifiedDataId: string,
        @Context() context: any
    ) {
        return this.certifiedContributionsService.create(
            createCertifiedContributionsInput,
            certifiedDataId
        )
    }
}
