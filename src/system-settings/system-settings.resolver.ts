import { Resolver, Query, Mutation, Args } from '@nestjs/graphql'
import { SystemSetting } from './entities/system-setting.entity'
import { CreateSystemSettingInput } from './dto/create-system-setting.input'
import { UpdateSystemSettingInput } from './dto/update-system-setting.input'
import { SystemSettingsService } from './system-settings.service'

@Resolver(() => SystemSetting)
export class SystemSettingsResolver {
    constructor(
        private readonly systemSettingsService: SystemSettingsService
    ) {}

    @Mutation(() => SystemSetting)
    createSystemSetting(
        @Args('createSystemSettingInput')
        createSystemSettingInput: CreateSystemSettingInput
    ) {
        return this.systemSettingsService.create(createSystemSettingInput)
    }

    @Query(() => [SystemSetting], { name: 'getAllSystemSettings' })
    findAll() {
        return this.systemSettingsService.findAll()
    }

    @Query(() => SystemSetting, { name: 'getSystemSettingById' })
    findOne(@Args('id', { type: () => String }) id: string) {
        return this.systemSettingsService.findOne(id)
    }

    @Query(() => SystemSetting, { name: 'getLatestSystemSetting' })
    findLatest() {
        return this.systemSettingsService.findLatest()
    }

    @Mutation(() => SystemSetting)
    updateSystemSetting(
        @Args('updateSystemSettingInput')
        updateSystemSettingInput: UpdateSystemSettingInput
    ): Promise<SystemSetting> {
        return this.systemSettingsService.update(
            updateSystemSettingInput.id,
            updateSystemSettingInput
        )
    }

    @Mutation(() => SystemSetting)
    removeSystemSetting(@Args('id', { type: () => String }) id: string) {
        return this.systemSettingsService.remove(id)
    }
}
