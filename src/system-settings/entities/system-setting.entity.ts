import { ObjectType, Field, Int } from '@nestjs/graphql'
import { User } from '../../user/entities/user.entity'

@ObjectType()
export class SystemSetting {
    @Field(() => String)
    id: string

    @Field(() => Boolean)
    autoApproveChanges: boolean

    @Field(() => Date)
    effectiveDate: Date

    @Field(() => Int)
    passwordExpiryDays: number

    @Field(() => Boolean)
    requireTwoFactorAuth: boolean

    @Field(() => Int)
    sessionTimeout: number

    @Field(() => User)
    updatedBy: User

    @Field(() => Date)
    updatedAt: Date

    @Field(() => Date)
    createdAt: Date

    @Field(() => String)
    userId: string
}
