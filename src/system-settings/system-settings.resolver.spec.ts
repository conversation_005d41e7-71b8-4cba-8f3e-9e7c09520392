import { Test, TestingModule } from '@nestjs/testing';
import { SystemSettingsResolver } from './system-settings.resolver';
import { SystemSettingsService } from './system-settings.service';

describe('SystemSettingsResolver', () => {
  let resolver: SystemSettingsResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [SystemSettingsResolver, SystemSettingsService],
    }).compile();

    resolver = module.get<SystemSettingsResolver>(SystemSettingsResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
