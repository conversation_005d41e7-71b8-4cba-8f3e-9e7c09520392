import { InputType, Field, Int } from '@nestjs/graphql'
import {
    IsBoolean,
    IsDate,
    IsInt,
    IsNotEmpty,
    IsUUID,
    Min,
} from 'class-validator'

@InputType()
export class CreateSystemSettingInput {
    @Field(() => Boolean, { defaultValue: false })
    @IsBoolean()
    autoApproveChanges: boolean

    @Field(() => Date)
    @IsDate()
    @IsNotEmpty()
    effectiveDate: Date

    @Field(() => Int, { defaultValue: 90 })
    @IsInt()
    @Min(1)
    passwordExpiryDays: number

    @Field(() => Boolean, { defaultValue: true })
    @IsBoolean()
    requireTwoFactorAuth: boolean

    @Field(() => Int, { defaultValue: 30 })
    @IsInt()
    @Min(1)
    sessionTimeout: number

    @Field(() => String)
    @IsUUID()
    @IsNotEmpty()
    userId: string
}
