import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateSystemSettingInput } from './dto/create-system-setting.input'
import { UpdateSystemSettingInput } from './dto/update-system-setting.input'

@Injectable()
export class SystemSettingsService {
    constructor(private readonly prisma: PrismaService) {}

    async create(createSystemSettingsInput: CreateSystemSettingInput) {
        return this.prisma.systemSettings.create({
            data: createSystemSettingsInput,
            include: {
                updatedBy: true,
            },
        })
    }

    async findAll() {
        return this.prisma.systemSettings.findMany({
            include: {
                updatedBy: true,
            },
        })
    }

    async findOne(id: string) {
        const systemSettings = await this.prisma.systemSettings.findUnique({
            where: { id },
            include: {
                updatedBy: true,
            },
        })

        if (!systemSettings) {
            throw new NotFoundException(
                `System settings with ID "${id}" not found`
            )
        }

        return systemSettings
    }

    async findLatest() {
        const latestSettings = await this.prisma.systemSettings.findFirst({
            orderBy: {
                effectiveDate: 'desc',
            },
            include: {
                updatedBy: true,
            },
        })

        if (!latestSettings) {
            throw new NotFoundException('No system settings found')
        }

        return latestSettings
    }

    async update(
        id: string,
        updateSystemSettingsInput: UpdateSystemSettingInput
    ) {
        await this.findOne(id)

        return this.prisma.systemSettings.update({
            where: { id },
            data: updateSystemSettingsInput,
            include: {
                updatedBy: true,
            },
        })
    }

    async remove(id: string) {
        await this.findOne(id)

        return this.prisma.systemSettings.delete({
            where: { id },
            include: {
                updatedBy: true,
            },
        })
    }
}
