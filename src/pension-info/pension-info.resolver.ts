import { Resolver, Query, Mutation, Args, ID } from '@nestjs/graphql'
import { PensionInfoService } from './pension-info.service'
import { CreatePensionInfoInput } from './dto/create-pension-info.input'
import { UpdatePensionInfoInput } from './dto/update-pension-info.input'
import { PensionInfo } from './entities/pension-info.entity'

@Resolver(() => PensionInfo)
export class PensionInfoResolver {
    constructor(private readonly pensionInfoService: PensionInfoService) {}

    @Mutation(() => PensionInfo)
    createPensionInfo(
        @Args('createPensionInfoInput')
        createPensionInfoInput: CreatePensionInfoInput
    ) {
        return this.pensionInfoService.create(createPensionInfoInput)
    }

    @Query(() => [PensionInfo], { name: 'pensionInfos' })
    findAll() {
        return this.pensionInfoService.findAll()
    }

    @Query(() => PensionInfo, { name: 'pensionInfo' })
    findOne(@Args('id', { type: () => ID }) id: string) {
        return this.pensionInfoService.findOne(id)
    }

    @Mutation(() => PensionInfo)
    updatePensionInfo(
        @Args('updatePensionInfoInput')
        updatePensionInfoInput: UpdatePensionInfoInput
    ) {
        return this.pensionInfoService.update(updatePensionInfoInput)
    }

    @Mutation(() => PensionInfo)
    removePensionInfo(@Args('id', { type: () => ID }) id: string) {
        return this.pensionInfoService.remove(id)
    }
}
