export interface PensionCode {
    code: number
    description: string
    allowedTransitions: number[]
    impact: string
}

export const PENSION_CODES: PensionCode[] = [
    {
        code: 10,
        description: 'Active',
        allowedTransitions: [30, 11, 40, 70],
        impact: 'Active pension accrual and indexation',
    },
    {
        code: 11,
        description: 'Disabled',
        allowedTransitions: [40, 70],
        impact: 'InActive service but continued pension accrual and indexation',
    },
    {
        code: 30,
        description: 'Inactive',
        allowedTransitions: [11, 40, 70],
        impact: 'No further pension accrual, only indexation',
    },
    {
        code: 40,
        description: 'Retired',
        allowedTransitions: [70],
        impact: 'No further pension accrual, only indexation',
    },
    {
        code: 50,
        description: "Partner entitled to partner's pension",
        allowedTransitions: [70],
        impact: 'Partner will be added as new participant when pension starts with entitled indexation',
    },
    {
        code: 55,
        description: "Orphan entitled to orphan's pension",
        allowedTransitions: [70],
        impact: '<PERSON><PERSON><PERSON> will be added as new participant when pension starts',
    },
    {
        code: 70,
        description: 'No longer entitled to pension',
        allowedTransitions: [],
        impact: 'No pension accrual or payments (e.g., due to death)',
    },
]

export function getPensionCodeDescription(code: number): string | undefined {
    return PENSION_CODES.find((pc) => pc.code === code)?.description
}

export function getPensionCodeImpact(code: number): string | undefined {
    return PENSION_CODES.find((pc) => pc.code === code)?.impact
}
