import { ObjectType, Field, ID, Int, Float } from '@nestjs/graphql'
import { Participant } from '../../participant/entities/participant.entity'

@ObjectType()
export class PensionInfo {
    @Field(() => ID)
    id: string

    @Field(() => Participant)
    participant: Participant

    @Field(() => Int, { nullable: true })
    code?: number

    @Field(() => Int, { nullable: true })
    previousCode?: number

    @Field({ nullable: true })
    codeEffectiveDate?: Date

    @Field({ nullable: true })
    previousCodeEffectiveDate?: Date

    @Field({ nullable: true })
    codeDescription?: string

    @Field({ nullable: true })
    codeImpact?: string

    @Field(() => Float, { nullable: true })
    accruedGrossAnnualOldAgePension?: number

    @Field(() => Float, { nullable: true })
    accruedGrossAnnualPartnersPension?: number

    @Field(() => Float, { nullable: true })
    accruedGrossAnnualSinglesPension?: number

    @Field(() => Float, { nullable: true })
    attainableGrossAnnualOldAgePension?: number

    @Field(() => Float, { nullable: true })
    extraAccruedGrossAnnualOldAgePension?: number

    @Field(() => Float, { nullable: true })
    extraAccruedGrossAnnualPartnersPension?: number

    @Field(() => Float, { nullable: true })
    grossAnnualDisabilityPension?: number

    @Field(() => Float, { nullable: true })
    pensionBase?: number

    @Field(() => Float, { nullable: true })
    grossAnnualPension?: number

    @Field(() => [String], { nullable: true })
    pendingChanges?: string[]
}
