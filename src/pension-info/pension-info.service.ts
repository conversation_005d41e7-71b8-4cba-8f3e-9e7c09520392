import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreatePensionInfoInput } from './dto/create-pension-info.input'
import { UpdatePensionInfoInput } from './dto/update-pension-info.input'
import * as R from 'ramda'

@Injectable()
export class PensionInfoService {
    constructor(private readonly prisma: PrismaService) {}

    create(createPensionInfoInput: CreatePensionInfoInput) {
        return this.prisma.pensionInfo.create({
            data: createPensionInfoInput,
            include: {
                participant: true,
            },
        })
    }

    findAll() {
        return this.prisma.pensionInfo.findMany({
            include: {
                participant: true,
            },
        })
    }

    findOne(id: string) {
        const pensionInfo = this.prisma.pensionInfo.findUnique({
            where: { id },
            include: {
                participant: true,
            },
        })
        if (!pensionInfo) {
            throw new NotFoundException(`PensionInfo with ID ${id} not found`)
        }
        return pensionInfo
    }

    update(updatePensionInfoInput: UpdatePensionInfoInput) {
        const pensionInfo = this.prisma.pensionInfo.findUnique({
            where: { id: updatePensionInfoInput.id },
        })
        if (!pensionInfo) {
            throw new NotFoundException(
                `PensionInfo with ID ${updatePensionInfoInput.id} not found`
            )
        }

        return this.prisma.pensionInfo.update({
            where: { id: updatePensionInfoInput.id },
            data: updatePensionInfoInput,
            include: {
                participant: true,
            },
        })
    }

    remove(id: string) {
        const pensionInfo = this.prisma.pensionInfo.findUnique({
            where: { id },
        })
        if (!pensionInfo) {
            throw new NotFoundException(`PensionInfo with ID ${id} not found`)
        }
        return this.prisma.pensionInfo.delete({
            where: { id },
            include: {
                participant: true,
            },
        })
    }
    async updatePendingChanges(id: string, paths: string[]) {
        const pensionInfo = await this.prisma.pensionInfo.findUnique({
            where: { id },
        })

        if (!pensionInfo) {
            throw new Error('PensionInfo not found')
        }

        const currentPendingChanges = pensionInfo.pendingChanges || []
        const updatedPendingChanges = R.uniq([
            ...currentPendingChanges,
            ...paths,
        ])

        await this.prisma.pensionInfo.update({
            where: { id },
            data: {
                pendingChanges: updatedPendingChanges,
            },
        })
    }

    async changeUpdate({
        entityId,
        path,
        newValue,
    }: {
        entityId: string
        path: string
        newValue: any
    }) {
        const pensionInfo = await this.prisma.pensionInfo.findUnique({
            where: { id: entityId },
        })
        const floatNewVal = parseFloat(newValue)

        if (!pensionInfo) {
            throw new Error('PensionInfo not found')
        }

        const updateData = path
            .split('.')
            .reduceRight<any>((acc, key) => ({ [key]: acc }), floatNewVal)

        await this.prisma.pensionInfo.update({
            where: { id: entityId },
            data: updateData,
        })
    }

    async clearPendingChanges(id: string, paths: string[]) {
        const pensionInfo = await this.prisma.pensionInfo.findUnique({
            where: { id },
        })

        if (!pensionInfo) {
            throw new Error('PensionInfo not found')
        }

        const currentPendingChanges = pensionInfo.pendingChanges || []
        const updatedPendingChanges = currentPendingChanges.filter(
            (path) => !paths.includes(path)
        )

        await this.prisma.pensionInfo.update({
            where: { id },
            data: {
                pendingChanges: updatedPendingChanges,
            },
        })
    }

    async updateFieldByParticipantId(data: {
        participantId: string
        path: string
        newValue: any
    }) {
        const { participantId, path, newValue } = data

        // Find the pension info record by participant ID
        const pensionInfo = await this.prisma.pensionInfo.findFirst({
            where: { participantId },
        })

        if (!pensionInfo) {
            throw new NotFoundException(
                `PensionInfo with participant ID ${participantId} not found`
            )
        }

        // Update the specific field
        const updateData = {
            [path]: newValue,
        }

        return this.prisma.pensionInfo.update({
            where: { id: pensionInfo.id },
            data: updateData,
        })
    }
}
