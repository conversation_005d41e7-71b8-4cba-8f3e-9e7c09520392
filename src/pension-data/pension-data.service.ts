import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreatePensionDataInput } from './dto/create-pension-data.input'
import { UpdatePensionDataInput } from './dto/update-pension-data.input'
import { PensionData } from './entities/pension-data.entity'

@Injectable()
export class PensionDataService {
    constructor(private readonly prisma: PrismaService) {}

    async create(createPensionDataInput: CreatePensionDataInput) {
        const pensionData = await this.prisma.pensionData.create({
            data: {
                ...createPensionDataInput,
            },
            include: {
                participant: true,
                pensionParameters: true,
                voluntaryContributions: true,
                annualAccrual: true,
                conversionDetails: true,
            },
        })

        if (!pensionData) {
            throw new NotFoundException('PensionData not created')
        }

        return pensionData
    }

    async findAll() {
        return this.prisma.pensionData.findMany({
            include: {
                participant: true,
                pensionParameters: true,
                voluntaryContributions: true,
                annualAccrual: true,
                conversionDetails: true,
            },
        })
    }

    async findOne(id: string) {
        const pensionData = await this.prisma.pensionData.findUnique({
            where: { id },
            include: {
                participant: true,
                pensionParameters: true,
                voluntaryContributions: true,
                annualAccrual: true,
                conversionDetails: true,
            },
        })

        if (!pensionData) {
            throw new NotFoundException('PensionData not found')
        }

        return pensionData
    }

    async update(updatePensionDataInput: UpdatePensionDataInput) {
        const pensionData = await this.prisma.pensionData.update({
            where: { id: updatePensionDataInput.id },
            data: {
                ...updatePensionDataInput,
            },
            include: {
                participant: true,
                pensionParameters: true,
                voluntaryContributions: true,
                annualAccrual: true,
                conversionDetails: true,
            },
        })

        if (!pensionData) {
            throw new NotFoundException('PensionData not updated')
        }

        return pensionData
    }

    async delete(id: string) {
        const pensionData = await this.prisma.pensionData.delete({
            where: { id },
        })

        if (!pensionData) {
            throw new NotFoundException('PensionData not deleted')
        }

        return pensionData
    }
}
