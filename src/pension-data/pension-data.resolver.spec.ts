import { Test, TestingModule } from '@nestjs/testing';
import { PensionDataResolver } from './pension-data.resolver';
import { PensionDataService } from './pension-data.service';

describe('PensionDataResolver', () => {
  let resolver: PensionDataResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [PensionDataResolver, PensionDataService],
    }).compile();

    resolver = module.get<PensionDataResolver>(PensionDataResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
