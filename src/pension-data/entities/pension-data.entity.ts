import { ObjectType, Field, ID } from '@nestjs/graphql'
import { Participant } from '../../participant/entities/participant.entity'
import { VoluntaryContribution } from '../../voluntary-contribution/entities/voluntary-contribution.entity'
import { AnnualAccrual } from '../../annual-accrual/entities/annual-accrual.entity'
import { ConversionDetail } from '../../conversion-details/entities/conversion-detail.entity'
import { PensionParameters } from '../../pension-parameters/entities/pension-parameter.entity'

@ObjectType()
export class PensionData {
    @Field(() => ID)
    id: string

    @Field(() => Participant)
    participant: Participant

    @Field({ nullable: true })
    status?: string

    @Field({ nullable: true })
    retirementDate?: Date

    @Field({ nullable: true })
    pensionableAmount?: number

    @Field({ nullable: true })
    totalContributions?: number

    @Field(() => PensionParameters, { nullable: true })
    pensionParameters?: PensionParameters

    @Field(() => [VoluntaryContribution])
    voluntaryContributions: VoluntaryContribution[]

    @Field(() => AnnualAccrual, { nullable: true })
    annualAccrual?: AnnualAccrual

    @Field(() => ConversionDetail, { nullable: true })
    conversionDetails?: ConversionDetail
}
