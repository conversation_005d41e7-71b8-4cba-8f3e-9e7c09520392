import { Resolver, Query, Mutation, Args } from '@nestjs/graphql'
import { PensionDataService } from './pension-data.service'
import { PensionData } from './entities/pension-data.entity'
import { CreatePensionDataInput } from './dto/create-pension-data.input'
import { UpdatePensionDataInput } from './dto/update-pension-data.input'

@Resolver(() => PensionData)
export class PensionDataResolver {
    constructor(private readonly pensionDataService: PensionDataService) {}

    @Mutation(() => PensionData)
    async createPensionData(
        @Args('createPensionDataInput')
        createPensionDataInput: CreatePensionDataInput
    ) {
        return this.pensionDataService.create(createPensionDataInput)
    }

    @Query(() => [PensionData], { name: 'pensionData' })
    async findAll() {
        return this.pensionDataService.findAll()
    }

    @Query(() => PensionData, { name: 'pensionData' })
    async findOne(@Args('id') id: string) {
        return this.pensionDataService.findOne(id)
    }

    @Mutation(() => PensionData)
    async updatePensionData(
        @Args('updatePensionDataInput')
        updatePensionDataInput: UpdatePensionDataInput
    ) {
        return this.pensionDataService.update(updatePensionDataInput)
    }

    @Mutation(() => PensionData)
    async deletePensionData(@Args('id') id: string) {
        return this.pensionDataService.delete(id)
    }
}
