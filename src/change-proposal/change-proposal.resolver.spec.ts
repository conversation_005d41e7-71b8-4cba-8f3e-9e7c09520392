import { Test, TestingModule } from '@nestjs/testing';
import { ChangeProposalResolver } from './change-proposal.resolver';
import { ChangeProposalService } from './change-proposal.service';

describe('ChangeProposalResolver', () => {
  let resolver: ChangeProposalResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ChangeProposalResolver, ChangeProposalService],
    }).compile();

    resolver = module.get<ChangeProposalResolver>(ChangeProposalResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
