import { ObjectType, Field, registerEnumType } from '@nestjs/graphql'
import { User } from '../../user/entities/user.entity'
import { ChangeStatus } from '@prisma/client'
import { ChangeData } from '../../change-data/entities/change-data.entity'

registerEnumType(ChangeStatus, {
    name: 'ChangeStatus',
})

@ObjectType()
export class ChangeProposal {
    @Field()
    id: string

    @Field()
    createdAt: Date

    @Field(() => User)
    createdBy: User

    @Field()
    effectiveDate: Date

    @Field()
    entityId: string

    @Field()
    entityType: string

    @Field(() => ChangeStatus)
    status: ChangeStatus

    @Field({ nullable: true })
    reviewComments?: string

    @Field({ nullable: true })
    participantName?: string

    @Field({ nullable: true })
    reviewedAt?: Date

    @Field(() => User, { nullable: true })
    reviewedBy?: User

    @Field()
    updatedAt: Date

    @Field(() => [ChangeData])
    changes: ChangeData[]

    @Field({ nullable: true })
    isCertificationProposal?: boolean

    @Field({ defaultValue: false })
    changePropagated: boolean
}
