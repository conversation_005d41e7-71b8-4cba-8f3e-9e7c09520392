import { InputType, Field, registerEnumType } from '@nestjs/graphql'
import { ChangeStatus, ChangeType } from '@prisma/client'
import {
    IsBoolean,
    IsDate,
    IsEnum,
    IsOptional,
    IsString,
} from 'class-validator'

registerEnumType(ChangeStatus, {
    name: 'ChangeStatus',
})

registerEnumType(ChangeType, {
    name: 'ChangeType',
})

@InputType()
export class CreateChangeProposalInput {
    @IsString()
    @Field()
    createdById: string

    @IsDate()
    @Field()
    effectiveDate: Date

    @IsString()
    @Field()
    entityId: string

    @IsString()
    @Field()
    entityType: string

    @IsEnum(ChangeType)
    @Field(() => ChangeType, { nullable: true })
    type: ChangeType

    @IsEnum(ChangeStatus)
    @Field(() => ChangeStatus, { nullable: true })
    status?: ChangeStatus

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    reviewComments?: string

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    reviewedById?: string

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    employmentInfoId?: string

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    participantName?: string

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    isCertificationProposal?: boolean

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true, defaultValue: false })
    changePropagated?: boolean
}
