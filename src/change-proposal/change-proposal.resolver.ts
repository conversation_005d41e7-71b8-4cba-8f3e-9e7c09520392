import { Resolver, Query, Mutation, Args } from '@nestjs/graphql'
import { ChangeProposalService } from './change-proposal.service'
import { ChangeProposal } from './entities/change-proposal.entity'
import { CreateChangeProposalInput } from './dto/create-change-proposal.input'
import { UpdateChangeProposalInput } from './dto/update-change-proposal.input'
import { CreateChangeDataInput } from '../change-data/dto/create-change-data.input'

@Resolver(() => ChangeProposal)
export class ChangeProposalResolver {
    constructor(
        private readonly changeProposalService: ChangeProposalService
    ) {}

    @Mutation(() => ChangeProposal)
    async createChangeProposal(
        @Args('createChangeProposalInput')
        createChangeProposalInput: CreateChangeProposalInput,
        @Args('createChangeDataInput', { nullable: true })
        createChangeDataInput?: CreateChangeDataInput
    ) {
        return this.changeProposalService.create(
            createChangeProposalInput,
            createChangeDataInput
        )
    }

    @Query(() => [ChangeProposal], { name: 'getAllChangeProposals' })
    async findAll() {
        return this.changeProposalService.findAll()
    }

    @Query(() => [ChangeProposal], { name: 'getAllParticipantChangeProposals' })
    async findByReviewerForParticipants(
        @Args('reviewerId') reviewerId: string
    ) {
        return this.changeProposalService.findByReviewerForParticipant()
    }
    @Query(() => [ChangeProposal], {
        name: 'getAllPensionParamChangeProposals',
    })
    async findByReviewerForPensionParams(
        @Args('reviewerId') reviewerId: string
    ) {
        return this.changeProposalService.findByReviewerForPensionParams()
    }

    @Query(() => [ChangeProposal], {
        name: 'getAllParticipantChangeProposalsHistory',
    })
    async findByReviewerForParticipantsHistory(
        @Args('reviewerId') reviewerId: string
    ) {
        return this.changeProposalService.findByReviewerForParticipantHistory(
            reviewerId
        )
    }
    @Query(() => [ChangeProposal], {
        name: 'getAllPensionParamChangeProposalsHistory',
    })
    async findByReviewerForPensionParamsHistory(
        @Args('reviewerId') reviewerId: string
    ) {
        return this.changeProposalService.findByReviewerForPensionParamsHistory(
            reviewerId
        )
    }

    @Query(() => ChangeProposal, { name: 'findOneChangeProposal' })
    async findOne(@Args('id') id: string) {
        return this.changeProposalService.findOne(id)
    }

    @Mutation(() => ChangeProposal)
    async updateChangeProposal(
        @Args('updateChangeProposalInput')
        updateChangeProposalInput: UpdateChangeProposalInput
    ) {
        return this.changeProposalService.update(updateChangeProposalInput)
    }

    @Mutation(() => ChangeProposal)
    async approveChangeProposal(
        @Args('changeProposalId')
        changeProposalId: string,
        @Args('reviewerId') reviewerId: string,
        @Args('changePropagated', { nullable: true, defaultValue: false })
        changePropagated: boolean = false
    ) {
        return this.changeProposalService.approve(
            changeProposalId,
            reviewerId,
            changePropagated
        )
    }

    @Mutation(() => ChangeProposal)
    async rejectChangeProposal(
        @Args('changeProposalId')
        changeProposalId: string,
        @Args('reviewerComments') reviewerComments: string,
        @Args('reviewerId') reviewerId: string
    ) {
        return this.changeProposalService.reject(
            changeProposalId,
            reviewerId,
            reviewerComments
        )
    }

    @Mutation(() => ChangeProposal, {
        name: 'getLatestApprovedChange',
        nullable: true,
    })
    async getLatestApprovedChange(
        @Args('entityType') entityType: string,
        @Args('path') path: string
    ) {
        return this.changeProposalService.getLatestApprovedChange(
            entityType,
            path
        )
    }

    @Mutation(() => ChangeProposal)
    async deleteChangeProposal(@Args('id') id: string) {
        return this.changeProposalService.delete(id)
    }
}
