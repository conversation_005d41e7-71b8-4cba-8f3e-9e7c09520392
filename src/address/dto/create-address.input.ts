import { InputType, Field } from '@nestjs/graphql'

@InputType()
export class CreateAddressInput {
    @Field()
    personalInfoId: string

    @Field({ nullable: true })
    street?: string

    @Field({ nullable: true })
    houseNumber?: string

    @Field({ nullable: true })
    postalCode?: string

    @Field({ nullable: true })
    city?: string

    @Field({ nullable: true })
    state?: string

    @Field({ nullable: true })
    country?: string
}
