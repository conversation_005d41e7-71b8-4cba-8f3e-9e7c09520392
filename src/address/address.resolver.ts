import { Resolver, Query, Mutation, Args } from '@nestjs/graphql'
import { AddressService } from './address.service'
import { Address } from './entities/address.entity'
import { CreateAddressInput } from './dto/create-address.input'
import { UpdateAddressInput } from './dto/update-address.input'

@Resolver(() => Address)
export class AddressResolver {
    constructor(private readonly addressService: AddressService) {}

    @Mutation(() => Address)
    async createAddress(
        @Args('createAddressInput') createAddressInput: CreateAddressInput
    ) {
        return this.addressService.create(createAddressInput)
    }

    @Query(() => [Address], { name: 'addresses' })
    async findAll() {
        return this.addressService.findAll()
    }

    @Query(() => Address, { name: 'address' })
    async findOne(@Args('id') id: string) {
        return this.addressService.findOne(id)
    }

    @Mutation(() => Address)
    async updateAddress(
        @Args('updateAddressInput') updateAddressInput: UpdateAddressInput
    ) {
        return this.addressService.update(updateAddressInput)
    }

    @Mutation(() => Address)
    async deleteAddress(@Args('id') id: string) {
        return this.addressService.delete(id)
    }
}
