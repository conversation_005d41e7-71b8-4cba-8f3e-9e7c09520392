import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateAddressInput } from './dto/create-address.input'
import { UpdateAddressInput } from './dto/update-address.input'
import { Address } from './entities/address.entity'

@Injectable()
export class AddressService {
    constructor(private readonly prisma: PrismaService) {}

    async create(createAddressInput: CreateAddressInput) {
        const address = await this.prisma.address.create({
            data: {
                ...createAddressInput,
            },
            include: {
                personalInfo: true,
            },
        })

        if (!address) {
            throw new NotFoundException('Address not created')
        }

        return address
    }

    async findAll() {
        return this.prisma.address.findMany({
            include: {
                personalInfo: true,
            },
        })
    }

    async findOne(id: string) {
        const address = await this.prisma.address.findUnique({
            where: { id },
            include: {
                personalInfo: true,
            },
        })

        if (!address) {
            throw new NotFoundException('Address not found')
        }

        return address
    }

    async update(updateAddressInput: UpdateAddressInput) {
        const address = await this.prisma.address.update({
            where: { id: updateAddressInput.id },
            data: {
                ...updateAddressInput,
            },
            include: {
                personalInfo: true,
            },
        })

        if (!address) {
            throw new NotFoundException('Address not updated')
        }

        return address
    }

    async updatePendingChanges(id: string, changes: string[]) {
        const addressInfo = await this.prisma.address.findUnique({
            where: { id },
        })

        if (!addressInfo) {
            throw new NotFoundException(`Child with ID ${id} not found`)
        }

        const currentChanges = addressInfo.pendingChanges || []
        const uniqueChanges = [...new Set([...currentChanges, ...changes])]

        const addressUpdatedInfo = await this.prisma.address.update({
            where: { id },
            data: {
                pendingChanges: uniqueChanges,
            },
        })

        return addressUpdatedInfo
    }

    async changeUpdate(changes: any) {
        const { entityId, newValue, path } = changes

        const updateData = {
            [path]: newValue,
        }

        const updatedPartner = await this.prisma.address.update({
            where: { id: entityId },
            data: updateData,
            include: {
                personalInfo: true,
            },
        })
    }

    async clearPendingChanges(id: string, paths: string | string[]) {
        const pathsArray = Array.isArray(paths) ? paths : [paths]

        const toUpdate = await this.prisma.address.findUnique({
            where: { id },
        })

        if (!toUpdate) {
            throw new NotFoundException(`PersonalInfo with ID ${id} not found`)
        }

        const currentChanges = toUpdate.pendingChanges || []

        const updatedChanges = currentChanges.filter(
            (path) => !pathsArray.includes(path)
        )

        return this.prisma.address.update({
            where: { id },
            data: { pendingChanges: updatedChanges },
        })
    }

    async delete(id: string) {
        const address = await this.prisma.address.delete({
            where: { id },
        })

        if (!address) {
            throw new NotFoundException('Address not deleted')
        }

        return address
    }

    async updateFieldByParticipantId(data: {
        participantId: string
        path: string
        newValue: any
    }) {
        const { participantId, path, newValue } = data

        // Find the personal info associated with the participant
        const personalInfo = await this.prisma.personalInfo.findFirst({
            where: { participantId },
            include: { address: true },
        })

        if (!personalInfo || !personalInfo.address) {
            throw new NotFoundException(
                `Address for participant ID ${participantId} not found`
            )
        }

        // Update the specific field
        const updateData = {
            [path]: newValue,
        }

        return this.prisma.address.update({
            where: { id: personalInfo.address.id },
            data: updateData,
        })
    }
}
