import { InputType, Field } from '@nestjs/graphql'
import { IsNotEmpty, IsString, IsOptional } from 'class-validator'

@InputType()
export class CreateChangeDataInput {
    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    changeProposalId: string

    @Field()
    @IsString()
    @IsNotEmpty()
    path: string

    @Field()
    @IsString()
    newValue: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    oldValue?: string
}
