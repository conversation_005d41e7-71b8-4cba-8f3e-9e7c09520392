import { ObjectType, Field, ID } from '@nestjs/graphql'
import { GraphQLJSON } from 'graphql-type-json'
import { ChangeProposal } from '../../change-proposal/entities/change-proposal.entity'

@ObjectType()
export class ChangeData {
    @Field(() => ID)
    id: string

    @Field(() => ChangeProposal)
    changeProposal: ChangeProposal

    @Field()
    path: string

    @Field(() => GraphQLJSON)
    newValue: any

    @Field(() => GraphQLJSON, { nullable: true })
    oldValue?: any
}
