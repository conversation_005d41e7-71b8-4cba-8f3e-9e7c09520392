import { Resolver, Mutation, Args } from '@nestjs/graphql'
import { ChangeDataService } from './change-data.service'
import { ChangeData } from './entities/change-data.entity'
import { CreateChangeDataInput } from './dto/create-change-data.input'
import { UpdateChangeDataInput } from './dto/update-change-data.input'

@Resolver(() => ChangeData)
export class ChangeDataResolver {
    constructor(private readonly changeDataService: ChangeDataService) {}

    @Mutation(() => ChangeData)
    async createChangeData(
        @Args('createChangeDataInput')
        createChangeDataInput: CreateChangeDataInput
    ) {
        return this.changeDataService.create(createChangeDataInput)
    }

    @Mutation(() => ChangeData)
    async updateChangeData(
        @Args('updateChangeDataInput')
        updateChangeDataInput: UpdateChangeDataInput
    ) {
        return this.changeDataService.update(updateChangeDataInput)
    }

    @Mutation(() => ChangeData)
    async deleteChangeData(@Args('id') id: string) {
        return this.changeDataService.delete(id)
    }
}
