import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateChangeDataInput } from './dto/create-change-data.input'
import { UpdateChangeDataInput } from './dto/update-change-data.input'
import { ChangeData } from './entities/change-data.entity'

@Injectable()
export class ChangeDataService {
    constructor(private readonly prisma: PrismaService) {}

    async create(createChangeDataInput: CreateChangeDataInput) {
        const changeData = await this.prisma.changeData.create({
            data: {
                ...createChangeDataInput,
            },
            include: {
                changeProposal: true,
            },
        })

        if (!changeData) {
            throw new NotFoundException('ChangeData not created')
        }

        return changeData
    }

    async update(updateChangeDataInput: UpdateChangeDataInput) {
        const changeData = await this.prisma.changeData.update({
            where: { id: updateChangeDataInput.id },
            data: {
                ...updateChangeDataInput,
            },
            include: {
                changeProposal: true,
            },
        })

        if (!changeData) {
            throw new NotFoundException('ChangeData not updated')
        }

        return changeData
    }

    async delete(id: string) {
        const changeData = await this.prisma.changeData.delete({
            where: { id },
        })

        if (!changeData) {
            throw new NotFoundException('ChangeData not deleted')
        }

        return changeData
    }
}
