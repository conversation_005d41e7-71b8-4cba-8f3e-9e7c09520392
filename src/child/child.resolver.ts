import { Resolver, Query, Mutation, Args } from '@nestjs/graphql'
import { ChildService } from './child.service'
import { Child } from './entities/child.entity'
import { CreateChildInput } from './dto/create-child.input'
import { UpdateChildInput } from './dto/update-child.input'

@Resolver(() => Child)
export class ChildResolver {
    constructor(private readonly childService: ChildService) {}

    @Mutation(() => Child)
    async createChild(
        @Args('createChildInput') createChildInput: CreateChildInput
    ) {
        return this.childService.create(createChildInput)
    }

    @Query(() => [Child], { name: 'getPensionChildren' })
    async findAll() {
        return this.childService.findAll()
    }

    @Query(() => Child, { name: 'child' })
    async findOne(@Args('id') id: string) {
        return this.childService.findOne(id)
    }

    @Mutation(() => Child)
    async updateChild(
        @Args('updateChildInput') updateChildInput: UpdateChildInput
    ) {
        return this.childService.update(updateChildInput)
    }

    @Mutation(() => Child)
    async deleteChild(@Args('id') id: string) {
        return this.childService.delete(id)
    }
}
