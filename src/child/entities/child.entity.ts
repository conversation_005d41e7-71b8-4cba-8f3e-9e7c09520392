import { ObjectType, Field } from '@nestjs/graphql'
import { PersonalInfo } from '../../personal-info/entities/personal-info.entity'

@ObjectType()
export class Child {
    @Field()
    id: string

    @Field(() => PersonalInfo)
    personalInfo: PersonalInfo

    @Field({ nullable: true })
    firstName?: string

    @Field({ nullable: true })
    lastName?: string

    @Field({ nullable: true })
    dateOfBirth?: Date

    @Field()
    isOrphan: boolean

    @Field()
    isStudying: boolean

    @Field(() => [String], { nullable: true })
    pendingChanges?: string[]
}
