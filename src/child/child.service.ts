import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { BaseService } from '../common/base.service'
import { CreateChildInput } from './dto/create-child.input'
import { UpdateChildInput } from './dto/update-child.input'

@Injectable()
export class ChildService extends BaseService {
    constructor(private readonly prisma: PrismaService) {
        super()
    }

    async create(createChildInput: CreateChildInput) {
        const child = await this.prisma.child.create({
            data: {
                ...createChildInput,
            },
            include: {
                personalInfo: true,
            },
        })

        if (!child) {
            throw new NotFoundException('Child not created')
        }

        return child
    }

    async findAll() {
        return this.prisma.child.findMany({
            include: {
                personalInfo: true,
            },
        })
    }

    async findOne(id: string) {
        const child = await this.prisma.child.findUnique({
            where: { id },
            include: {
                personalInfo: true,
            },
        })

        if (!child) {
            throw new NotFoundException('Child not found')
        }

        return child
    }

    async update(updateChildInput: UpdateChildInput) {
        const child = await this.prisma.child.update({
            where: { id: updateChildInput.id },
            data: {
                ...updateChildInput,
            },
            include: {
                personalInfo: true,
            },
        })

        if (!child) {
            throw new NotFoundException('Child not updated')
        }

        return child
    }

    async updatePendingChanges(id: string, changes: string[]) {
        const childInfo = await this.prisma.child.findUnique({
            where: { id },
        })

        if (!childInfo) {
            throw new NotFoundException(`Child with ID ${id} not found`)
        }

        const currentChanges = childInfo.pendingChanges || []
        const uniqueChanges = [...new Set([...currentChanges, ...changes])]

        const childUpdatedInfo = await this.prisma.child.update({
            where: { id },
            data: {
                pendingChanges: uniqueChanges,
            },
        })

        return childUpdatedInfo
    }

    async changeUpdate(changes: any) {
        const { entityId, newValue, path } = changes

        const value = this.handleDateFields(path, newValue)

        const updateData = {
            [path]: value,
        }

        const updatedPartner = await this.prisma.child.update({
            where: { id: entityId },
            data: updateData,
            include: {
                personalInfo: true,
            },
        })

        return updatedPartner
    }

    async clearPendingChanges(id: string, paths: string | string[]) {
        const pathsArray = Array.isArray(paths) ? paths : [paths]

        const toUpdate = await this.prisma.child.findUnique({
            where: { id },
        })

        if (!toUpdate) {
            throw new NotFoundException(`PersonalInfo with ID ${id} not found`)
        }

        const currentChanges = toUpdate.pendingChanges || []

        const updatedChanges = currentChanges.filter(
            (path) => !pathsArray.includes(path)
        )

        return this.prisma.child.update({
            where: { id },
            data: { pendingChanges: updatedChanges },
        })
    }

    async delete(id: string) {
        const child = await this.prisma.child.delete({
            where: { id },
        })

        if (!child) {
            throw new NotFoundException('Child not deleted')
        }

        return child
    }

    async updateFieldByParticipantId(data: {
        participantId: string
        path: string
        newValue: any
    }) {
        const { participantId, path, newValue } = data

        // For Child entities, we need to find through personalInfo
        const personalInfo = await this.prisma.personalInfo.findFirst({
            where: { participantId },
            include: { children: true },
        })

        if (!personalInfo || !personalInfo.children?.length) {
            throw new NotFoundException(
                `No children found for participant ID ${participantId}`
            )
        }

        // This will update all children with the same field value
        // You might want to modify this logic based on specific requirements
        const updatePromises = personalInfo.children.map((child) =>
            this.prisma.child.update({
                where: { id: child.id },
                data: { [path]: newValue },
            })
        )

        const results = await Promise.all(updatePromises)
        return results[0] // Return the first updated child
    }
}
