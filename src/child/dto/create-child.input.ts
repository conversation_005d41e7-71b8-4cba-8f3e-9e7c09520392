import { InputType, Field } from '@nestjs/graphql'

@InputType()
export class CreateChildInput {
    @Field()
    personalInfoId: string

    @Field({ nullable: true })
    firstName?: string

    @Field({ nullable: true })
    lastName?: string

    @Field({ nullable: true })
    dateOfBirth?: Date

    @Field({ nullable: true })
    isOrphan?: boolean

    @Field({ nullable: true })
    isStudying?: boolean
}
