import { Resolver, Query, Mutation, Args, ID } from '@nestjs/graphql'
import { RoleService } from './role.service'
import { Role } from './entities/role.entity'
import { CreateRoleInput } from './dto/create-role.input'
import { UpdateRoleInput } from './dto/update-role.input'

@Resolver(() => Role)
export class RoleResolver {
    constructor(private readonly roleService: RoleService) {}

    @Mutation(() => Role)
    createRole(@Args('createRoleInput') createRoleInput: CreateRoleInput) {
        return this.roleService.create(createRoleInput)
    }

    @Query(() => [Role], { name: 'roles' })
    findAll() {
        return this.roleService.findAll()
    }

    @Query(() => Role, { name: 'role' })
    findOne(@Args('id', { type: () => ID }) id: string) {
        return this.roleService.findOne(id)
    }

    @Mutation(() => Role)
    updateRole(@Args('updateRoleInput') updateRoleInput: UpdateRoleInput) {
        return this.roleService.update(updateRoleInput)
    }

    @Mutation(() => Role)
    removeRole(@Args('id', { type: () => ID }) id: string) {
        return this.roleService.remove(id)
    }
}
