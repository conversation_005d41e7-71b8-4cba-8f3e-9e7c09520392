import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateRoleInput } from './dto/create-role.input'
import { UpdateRoleInput } from './dto/update-role.input'

@Injectable()
export class RoleService {
    constructor(private readonly prisma: PrismaService) {}

    create(createRoleInput: CreateRoleInput) {
        return this.prisma.role.create({
            data: createRoleInput,
            include: {
                users: true,
            },
        })
    }

    findAll() {
        return this.prisma.role.findMany({
            include: {
                users: true,
            },
        })
    }

    findOne(id: string) {
        const role = this.prisma.role.findUnique({
            where: { id },
            include: {
                users: true,
            },
        })
        if (!role) {
            throw new NotFoundException(`Role with ID ${id} not found`)
        }
        return role
    }

    update(updateRoleInput: UpdateRoleInput) {
        const role = this.prisma.role.findUnique({
            where: { id: updateRoleInput.id },
        })
        if (!role) {
            throw new NotFoundException(
                `Role with ID ${updateRoleInput.id} not found`
            )
        }

        return this.prisma.role.update({
            where: { id: updateRoleInput.id },
            data: updateRoleInput,
            include: {
                users: true,
            },
        })
    }

    remove(id: string) {
        const role = this.prisma.role.findUnique({
            where: { id },
        })
        if (!role) {
            throw new NotFoundException(`Role with ID ${id} not found`)
        }
        return this.prisma.role.delete({
            where: { id },
            include: {
                users: true,
            },
        })
    }
}
