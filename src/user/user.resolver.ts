import {
    Resolver,
    Query,
    Mutation,
    Args,
    ResolveField,
    Parent,
} from '@nestjs/graphql'
import { User } from './entities/user.entity'
import { CreateUserInput } from './dto/create-user.input'
import { UpdateUserInput } from './dto/update-user.input'
import { PrismaService } from '../prisma.service'
import { UserService } from './user.service'

@Resolver(() => User)
export class UserResolver {
    constructor(
        private readonly userService: UserService,
        private readonly prisma: PrismaService
    ) {}

    @Mutation(() => User)
    createUser(@Args('createUserInput') createUserInput: CreateUserInput) {
        return this.userService.create(createUserInput)
    }

    @Query(() => [User], { name: 'getAllUsers' })
    findAll() {
        return this.userService.findAll()
    }

    @Query(() => User, { name: 'getUserById' })
    findOne(@Args('id', { type: () => String }) id: string) {
        return this.userService.findOne(id)
    }

    @Query(() => User, { name: 'getUserByEmail' })
    findByEmail(@Args('email', { type: () => String }) email: string) {
        return this.userService.findByEmail(email)
    }

    @Query(() => User, { name: 'userByFirebaseUid' })
    findByFirebaseUid(
        @Args('firebaseUid', { type: () => String }) firebaseUid: string
    ) {
        return this.userService.findByFirebaseUid(firebaseUid)
    }

    @Mutation(() => User)
    updateUser(@Args('updateUserInput') updateUserInput: UpdateUserInput) {
        return this.userService.update(updateUserInput.id, updateUserInput)
    }

    @Mutation(() => User)
    updateUserLastLogin(@Args('id', { type: () => String }) id: string) {
        return this.userService.updateLastLogin(id)
    }

    @Mutation(() => User)
    removeUser(@Args('id', { type: () => String }) id: string) {
        return this.userService.remove(id)
    }
}
