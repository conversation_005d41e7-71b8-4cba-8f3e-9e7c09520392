import { InputType, Field } from '@nestjs/graphql'
import {
    IsEmail,
    IsNotEmpty,
    IsOptional,
    IsString,
    IsUUID,
} from 'class-validator'

@InputType()
export class CreateUserInput {
    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    firebaseUid?: string

    @Field(() => String)
    @IsEmail()
    @IsNotEmpty()
    email: string

    @Field(() => String, { nullable: true })
    @IsString()
    @IsOptional()
    firstname?: string

    @Field(() => String, { nullable: true })
    @IsString()
    @IsOptional()
    lastname?: string

    @Field(() => String)
    @IsUUID()
    @IsNotEmpty()
    roleId: string
}
