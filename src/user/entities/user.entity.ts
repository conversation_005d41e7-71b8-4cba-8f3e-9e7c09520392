import { ObjectType, Field, ID } from '@nestjs/graphql'
import { Role } from '../../role/entities/role.entity'

@ObjectType()
export class User {
    @Field(() => ID)
    id: string

    @Field(() => String)
    firebaseUid: string

    @Field(() => String)
    email: string

    @Field(() => String, { nullable: true })
    firstname?: string

    @Field(() => String, { nullable: true })
    lastname?: string

    @Field(() => Date, { nullable: true })
    lastLogin?: Date

    @Field(() => Role, { nullable: true })
    role?: Role
}
