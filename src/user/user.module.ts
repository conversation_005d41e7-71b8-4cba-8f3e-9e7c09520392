import { Module } from '@nestjs/common'
import { UserResolver } from './user.resolver'
import { UserService } from './user.service'
import { AuthModule } from '../auth/auth.module'
import { EmailHandlerModule } from '../email-handler/email-handler.module'

@Module({
    imports: [AuthModule, EmailHandlerModule],
    exports: [UserService],
    providers: [UserResolver, UserService],
})
export class UserModule {}
