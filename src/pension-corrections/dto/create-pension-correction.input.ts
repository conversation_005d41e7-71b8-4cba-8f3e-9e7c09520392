import {
    InputType,
    Field,
    Float,
    GraphQLISODateTime,
    ID,
} from '@nestjs/graphql'
import { IsNotEmpty, IsString, IsNumber, IsDateString } from 'class-validator'

@InputType()
export class CreatePensionCorrectionsInput {
    @Field(() => Float)
    @IsNumber()
    @IsNotEmpty()
    correction: number

    @Field()
    @IsString()
    @IsNotEmpty()
    year: string

    @Field(() => GraphQLISODateTime)
    @IsDateString()
    @IsNotEmpty()
    reviewedAt: Date

    @Field(() => ID)
    @IsString()
    @IsNotEmpty()
    createdById: string

    @Field(() => ID)
    @IsString()
    @IsNotEmpty()
    reviewedById: string
}
