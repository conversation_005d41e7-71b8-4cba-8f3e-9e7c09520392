import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreatePensionCorrectionsInput } from './dto/create-pension-correction.input'
import { UpdatePensionCorrectionsInput } from './dto/update-pension-correction.input'

@Injectable()
export class PensionCorrectionsService {
    constructor(private readonly prisma: PrismaService) {}

    create(createPensionCorrectionsInput: CreatePensionCorrectionsInput) {
        return this.prisma.pensionCorrections.create({
            data: createPensionCorrectionsInput,
            include: {
                createdBy: true,
                reviewedBy: true,
            },
        })
    }

    findAll() {
        return this.prisma.pensionCorrections.findMany({
            include: {
                createdBy: true,
                reviewedBy: true,
            },
        })
    }

    findOne(id: string) {
        const pensionCorrection = this.prisma.pensionCorrections.findUnique({
            where: { id },
            include: {
                createdBy: true,
                reviewedBy: true,
            },
        })
        if (!pensionCorrection) {
            throw new NotFoundException(
                `PensionCorrection with ID ${id} not found`
            )
        }
        return pensionCorrection
    }

    update(updatePensionCorrectionsInput: UpdatePensionCorrectionsInput) {
        const pensionCorrection = this.prisma.pensionCorrections.findUnique({
            where: { id: updatePensionCorrectionsInput.id },
        })
        if (!pensionCorrection) {
            throw new NotFoundException(
                `PensionCorrection with ID ${updatePensionCorrectionsInput.id} not found`
            )
        }

        return this.prisma.pensionCorrections.update({
            where: { id: updatePensionCorrectionsInput.id },
            data: updatePensionCorrectionsInput,
            include: {
                createdBy: true,
                reviewedBy: true,
            },
        })
    }

    remove(id: string) {
        const pensionCorrection = this.prisma.pensionCorrections.findUnique({
            where: { id },
        })
        if (!pensionCorrection) {
            throw new NotFoundException(
                `PensionCorrection with ID ${id} not found`
            )
        }
        return this.prisma.pensionCorrections.delete({
            where: { id },
            include: {
                createdBy: true,
                reviewedBy: true,
            },
        })
    }
}
