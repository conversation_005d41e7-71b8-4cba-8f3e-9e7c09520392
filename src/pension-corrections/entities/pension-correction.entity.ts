import {
    ObjectType,
    Field,
    ID,
    Float,
    GraphQLISODateTime,
} from '@nestjs/graphql'
import { User } from '../../user/entities/user.entity'

@ObjectType()
export class PensionCorrections {
    @Field(() => ID)
    id: string

    @Field(() => Float)
    correction: number

    @Field(() => GraphQLISODateTime)
    createdAt: Date

    @Field(() => User)
    createdBy: User

    @Field()
    year: string

    @Field(() => GraphQLISODateTime)
    reviewedAt: Date

    @Field(() => User)
    reviewedBy: User

    @Field(() => GraphQLISODateTime)
    updatedAt: Date
}
