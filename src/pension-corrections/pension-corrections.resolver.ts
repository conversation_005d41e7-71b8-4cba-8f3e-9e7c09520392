import { Resolver, Query, Mutation, Args, ID } from '@nestjs/graphql'
import { PensionCorrectionsService } from './pension-corrections.service'
import { PensionCorrections } from './entities/pension-correction.entity'
import { CreatePensionCorrectionsInput } from './dto/create-pension-correction.input'
import { UpdatePensionCorrectionsInput } from './dto/update-pension-correction.input'

@Resolver(() => PensionCorrections)
export class PensionCorrectionsResolver {
    constructor(
        private readonly pensionCorrectionsService: PensionCorrectionsService
    ) {}

    @Mutation(() => PensionCorrections)
    createPensionCorrections(
        @Args('createPensionCorrectionsInput')
        createPensionCorrectionsInput: CreatePensionCorrectionsInput
    ) {
        return this.pensionCorrectionsService.create(
            createPensionCorrectionsInput
        )
    }

    @Query(() => [PensionCorrections], { name: 'pensionCorrections' })
    findAll() {
        return this.pensionCorrectionsService.findAll()
    }

    @Query(() => PensionCorrections, { name: 'pensionCorrection' })
    findOne(@Args('id', { type: () => ID }) id: string) {
        return this.pensionCorrectionsService.findOne(id)
    }

    @Mutation(() => PensionCorrections)
    updatePensionCorrections(
        @Args('updatePensionCorrectionsInput')
        updatePensionCorrectionsInput: UpdatePensionCorrectionsInput
    ) {
        return this.pensionCorrectionsService.update(
            updatePensionCorrectionsInput
        )
    }

    @Mutation(() => PensionCorrections)
    removePensionCorrections(@Args('id', { type: () => ID }) id: string) {
        return this.pensionCorrectionsService.remove(id)
    }
}
