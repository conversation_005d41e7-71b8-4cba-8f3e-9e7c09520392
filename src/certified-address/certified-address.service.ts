import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'

@Injectable()
export class CertifiedAddressService {
    constructor(private readonly prisma: PrismaService) {}

    async findOne(id: string) {
        const certifiedAddress = await this.prisma.certifiedAddress.findUnique({
            where: { id },
            include: {
                certifiedData: true,
                certificationRejectReason: true,
            },
        })

        if (!certifiedAddress) {
            throw new NotFoundException(
                `CertifiedAddress with ID ${id} not found`
            )
        }

        return certifiedAddress
    }

    async changeUpdate(changes: any) {
        const { entityId, newValue, path } = changes

        const updateData = {
            [path]: newValue,
        }

        const updatedInfo = await this.prisma.certifiedAddress.update({
            where: { id: entityId },
            data: updateData,
            include: {
                certifiedData: true,
            },
        })

        if (!updatedInfo) {
            throw new NotFoundException(
                `CertifiedAddress with ID ${entityId} not updated`
            )
        }

        return updatedInfo
    }

    async updatePendingChanges(id: string, changes: string[]) {
        const certifiedInfo = await this.prisma.certifiedAddress.findUnique({
            where: { id },
        })

        if (!certifiedInfo) {
            throw new NotFoundException(
                `CertifiedAddress with ID ${id} not found`
            )
        }

        const currentChanges = certifiedInfo.pendingChanges || []
        const uniqueChanges = [...new Set([...currentChanges, ...changes])]

        const updatedInfo = await this.prisma.certifiedAddress.update({
            where: { id },
            data: {
                pendingChanges: uniqueChanges,
            },
        })

        return updatedInfo
    }

    async clearPendingChanges(id: string, paths: string | string[]) {
        const pathsArray = Array.isArray(paths) ? paths : [paths]

        const toUpdate = await this.prisma.certifiedAddress.findUnique({
            where: { id },
        })

        if (!toUpdate) {
            throw new NotFoundException(
                `CertifiedAddress with ID ${id} not found`
            )
        }

        const currentChanges = toUpdate.pendingChanges || []
        const updatedChanges = currentChanges.filter(
            (path) => !pathsArray.includes(path)
        )

        return this.prisma.certifiedAddress.update({
            where: { id },
            data: { pendingChanges: updatedChanges },
        })
    }
}
