import { Test, TestingModule } from '@nestjs/testing';
import { CertifiedAddressService } from './certified-address.service';

describe('CertifiedAddressService', () => {
  let service: CertifiedAddressService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CertifiedAddressService],
    }).compile();

    service = module.get<CertifiedAddressService>(CertifiedAddressService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
