import { Resolver, Query, Args } from '@nestjs/graphql'
import { CertifiedAddressService } from './certified-address.service'
import { CertifiedAddress } from '../certified-data/entities/certified-address.entity'

@Resolver(() => CertifiedAddress)
export class CertifiedAddressResolver {
    constructor(
        private readonly certifiedAddressService: CertifiedAddressService
    ) {}

    @Query(() => CertifiedAddress, { name: 'certifiedAddress' })
    findOne(@Args('id', { type: () => String }) id: string) {
        return this.certifiedAddressService.findOne(id)
    }
}
