import { Test, TestingModule } from '@nestjs/testing';
import { CertifiedAddressResolver } from './certified-address.resolver';
import { CertifiedAddressService } from './certified-address.service';

describe('CertifiedAddressResolver', () => {
  let resolver: CertifiedAddressResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CertifiedAddressResolver, CertifiedAddressService],
    }).compile();

    resolver = module.get<CertifiedAddressResolver>(CertifiedAddressResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
