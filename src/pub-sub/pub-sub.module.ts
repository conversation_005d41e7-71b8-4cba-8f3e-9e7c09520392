import {
    forwardRef,
    Module,
    MiddlewareConsumer,
    NestModule,
} from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'
import { RedisPubSub } from 'graphql-redis-subscriptions'
import Redis from 'ioredis'
import { json, urlencoded } from 'express'
import { PubSubController } from './pub-sub.controller'

@Module({
    imports: [ConfigModule],
    providers: [
        {
            provide: 'PUB_SUB',
            useFactory: (configService: ConfigService) => {
                const redisOptions = {
                    host: configService.get('REDIS_HOST', 'localhost'),
                    port: configService.get('REDIS_PORT', 6379),
                    username: configService.get('REDIS_USER'),
                    password: configService.get('REDIS_PASSWORD'),
                    retryStrategy: (times: number) => {
                        return Math.min(times * 50, 2000)
                    },
                }

                // return new RedisPubSub({
                //     publisher: new Redis(redisOptions).disconnect(),
                //     subscriber: new Redis(redisOptions).disconnect(),
                // })
            },
            inject: [ConfigService],
        },
    ],
    exports: ['PUB_SUB'],
})
export class PubSubModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {
        consumer
            .apply(json(), urlencoded({ extended: true }))
            .forRoutes(PubSubController)
    }
}
