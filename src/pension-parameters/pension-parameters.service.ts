import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreatePensionParametersInput } from './dto/create-pension-parameter.input'
import { UpdatePensionParametersInput } from './dto/update-pension-parameter.input'

@Injectable()
export class PensionParametersService {
    constructor(private readonly prisma: PrismaService) {}

    create(createPensionParametersInput: CreatePensionParametersInput) {
        return this.prisma.pensionParameters.create({
            data: createPensionParametersInput,
            include: {
                updatedBy: true,
                pensionData: true,
            },
        })
    }

    findAll() {
        return this.prisma.pensionParameters.findMany({
            include: {
                updatedBy: true,
                pensionData: true,
            },
        })
    }

    findOne(id: string) {
        const pensionParameters = this.prisma.pensionParameters.findUnique({
            where: { id },
            include: {
                updatedBy: true,
                pensionData: true,
            },
        })
        if (!pensionParameters) {
            throw new NotFoundException(
                `PensionParameters with ID ${id} not found`
            )
        }
        return pensionParameters
    }

    update(updatePensionParametersInput: UpdatePensionParametersInput) {
        const pensionParameters = this.prisma.pensionParameters.findUnique({
            where: { id: updatePensionParametersInput.id },
        })
        if (!pensionParameters) {
            throw new NotFoundException(
                `PensionParameters with ID ${updatePensionParametersInput.id} not found`
            )
        }

        return this.prisma.pensionParameters.update({
            where: { id: updatePensionParametersInput.id },
            data: updatePensionParametersInput,
            include: {
                updatedBy: true,
                pensionData: true,
            },
        })
    }

    async updatePendingChanges(id: string, changes: string[]) {
        const pensionParams = await this.prisma.pensionParameters.findUnique({
            where: { id },
        })

        if (!pensionParams) {
            throw new NotFoundException(`PensionParams with ID ${id} not found`)
        }

        const currentChanges = pensionParams.pendingChanges || []
        const uniqueChanges = [...new Set([...currentChanges, ...changes])]

        const pensionParamUpdatedInfo =
            await this.prisma.pensionParameters.update({
                where: { id },
                data: {
                    pendingChanges: uniqueChanges,
                },
            })

        return pensionParamUpdatedInfo
    }

    async changeUpdate(changes: any) {
        const { entityId, newValue, path } = changes

        const updateData = {
            [path]: Number(newValue),
        }

        const updatedPartner = await this.prisma.pensionParameters.update({
            where: { id: entityId },
            data: updateData,
        })
    }

    async clearPendingParameterChanges(id: string, paths: string | string[]) {
        const pathsArray = Array.isArray(paths) ? paths : [paths]

        const toUpdate = await this.prisma.pensionParameters.findUnique({
            where: { id },
        })

        if (!toUpdate) {
            throw new NotFoundException(`PensionParam with ID ${id} not found`)
        }

        const currentChanges = toUpdate.pendingChanges || []

        const updatedChanges = currentChanges.filter(
            (path) => !pathsArray.includes(path)
        )

        return this.prisma.pensionParameters.update({
            where: { id },
            data: { pendingChanges: updatedChanges },
        })
    }
    remove(id: string) {
        const pensionParameters = this.prisma.pensionParameters.findUnique({
            where: { id },
        })
        if (!pensionParameters) {
            throw new NotFoundException(
                `PensionParameters with ID ${id} not found`
            )
        }
        return this.prisma.pensionParameters.delete({
            where: { id },
            include: {
                updatedBy: true,
                pensionData: true,
            },
        })
    }
}
