import {
    InputType,
    Field,
    Float,
    Int,
    GraphQLISODateTime,
    ID,
} from '@nestjs/graphql'
import {
    IsNotEmpty,
    IsString,
    IsNumber,
    IsInt,
    IsDateString,
} from 'class-validator'

@InputType()
export class CreatePensionParametersInput {
    @Field(() => Float)
    @IsNumber()
    @IsNotEmpty()
    accrualPercentage: number

    @Field(() => Float)
    @IsNumber()
    @IsNotEmpty()
    annualMultiplier: number

    @Field(() => Float)
    @IsNumber()
    @IsNotEmpty()
    offsetAmount: number

    @Field(() => Float)
    @IsNumber()
    @IsNotEmpty()
    partnersPensionPercentage: number

    @Field(() => Int)
    @IsInt()
    @IsNotEmpty()
    retirementAge: number

    @Field(() => Float)
    @IsNumber()
    @IsNotEmpty()
    voluntaryContributionInterestRate: number

    @Field()
    @IsString()
    @IsNotEmpty()
    year: string

    @Field()
    @IsString()
    @IsNotEmpty()
    pensionDataId: string

    @Field(() => GraphQLISODateTime)
    @IsDateString()
    @IsNotEmpty()
    effectiveDate: Date

    @Field(() => ID)
    @IsString()
    @IsNotEmpty()
    userId: string
}
