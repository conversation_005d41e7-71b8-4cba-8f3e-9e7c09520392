import { Resolver, Query, Mutation, Args } from '@nestjs/graphql'
import { VoluntaryContributionService } from './voluntary-contribution.service'
import { VoluntaryContribution } from './entities/voluntary-contribution.entity'
import { CreateVoluntaryContributionInput } from './dto/create-voluntary-contribution.input'
import { UpdateVoluntaryContributionInput } from './dto/update-voluntary-contribution.input'

@Resolver(() => VoluntaryContribution)
export class VoluntaryContributionResolver {
    constructor(
        private readonly voluntaryContributionService: VoluntaryContributionService
    ) {}

    @Mutation(() => VoluntaryContribution)
    async createVoluntaryContribution(
        @Args('createVoluntaryContributionInput')
        createVoluntaryContributionInput: CreateVoluntaryContributionInput
    ) {
        return this.voluntaryContributionService.create(
            createVoluntaryContributionInput
        )
    }

    @Query(() => [VoluntaryContribution], { name: 'voluntaryContributions' })
    async findAll() {
        return this.voluntaryContributionService.findAll()
    }

    @Query(() => VoluntaryContribution, { name: 'voluntaryContribution' })
    async findOne(@Args('id') id: string) {
        return this.voluntaryContributionService.findOne(id)
    }

    @Mutation(() => VoluntaryContribution)
    async updateVoluntaryContribution(
        @Args('updateVoluntaryContributionInput')
        updateVoluntaryContributionInput: UpdateVoluntaryContributionInput
    ) {
        return this.voluntaryContributionService.update(
            updateVoluntaryContributionInput
        )
    }

    @Mutation(() => VoluntaryContribution)
    async deleteVoluntaryContribution(@Args('id') id: string) {
        return this.voluntaryContributionService.delete(id)
    }
}
