import { ObjectType, Field, ID } from '@nestjs/graphql'
import { PensionData } from '../../pension-data/entities/pension-data.entity'

@ObjectType()
export class VoluntaryContribution {
    @Field(() => ID)
    id: string

    @Field(() => PensionData)
    pensionData: PensionData

    @Field({ nullable: true })
    amount?: number

    @Field({ nullable: true })
    accumulatedInterest?: number

    @Field({ nullable: true })
    date?: Date

    @Field({ nullable: true })
    type?: string
}
