import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateVoluntaryContributionInput } from './dto/create-voluntary-contribution.input'
import { UpdateVoluntaryContributionInput } from './dto/update-voluntary-contribution.input'
import { VoluntaryContribution } from './entities/voluntary-contribution.entity'

@Injectable()
export class VoluntaryContributionService {
    constructor(private readonly prisma: PrismaService) {}

    async create(
        createVoluntaryContributionInput: CreateVoluntaryContributionInput
    ) {
        const voluntaryContribution =
            await this.prisma.voluntaryContribution.create({
                data: {
                    ...createVoluntaryContributionInput,
                },
                include: {
                    pensionData: true,
                },
            })

        if (!voluntaryContribution) {
            throw new NotFoundException('VoluntaryContribution not created')
        }

        return voluntaryContribution
    }

    async findAll() {
        return this.prisma.voluntaryContribution.findMany({
            include: {
                pensionData: true,
            },
        })
    }

    async findOne(id: string) {
        const voluntaryContribution =
            await this.prisma.voluntaryContribution.findUnique({
                where: { id },
                include: {
                    pensionData: true,
                },
            })

        if (!voluntaryContribution) {
            throw new NotFoundException('VoluntaryContribution not found')
        }

        return voluntaryContribution
    }

    async update(
        updateVoluntaryContributionInput: UpdateVoluntaryContributionInput
    ) {
        const voluntaryContribution =
            await this.prisma.voluntaryContribution.update({
                where: { id: updateVoluntaryContributionInput.id },
                data: {
                    ...updateVoluntaryContributionInput,
                },
                include: {
                    pensionData: true,
                },
            })

        if (!voluntaryContribution) {
            throw new NotFoundException('VoluntaryContribution not updated')
        }

        return voluntaryContribution
    }

    async delete(id: string) {
        const voluntaryContribution =
            await this.prisma.voluntaryContribution.delete({
                where: { id },
            })

        if (!voluntaryContribution) {
            throw new NotFoundException('VoluntaryContribution not deleted')
        }

        return voluntaryContribution
    }
}
