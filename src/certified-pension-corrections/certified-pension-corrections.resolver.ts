import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql'
import { UseGuards } from '@nestjs/common'
import { CertifiedPensionCorrectionsService } from './certified-pension-corrections.service'
import { CertifiedPensionCorrections } from '../certified-data/entities/certified-pension-corrections.entity'
import { CreateCertifiedPensionCorrectionsInput } from '../certified-data/dto/create-certified-pension-corrections.input'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'

@Resolver(() => CertifiedPensionCorrections)
export class CertifiedPensionCorrectionsResolver {
    constructor(
        private readonly certifiedCorrectionsService: CertifiedPensionCorrectionsService
    ) {}

    @UseGuards(GraphqlAuthGuard)
    @Query(() => CertifiedPensionCorrections)
    async certifiedPensionCorrections(@Args('id') id: string) {
        return this.certifiedCorrectionsService.findOne(id)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => CertifiedPensionCorrections)
    async certifiedCorrectionsByCertifiedDataId(
        @Args('certifiedDataId') certifiedDataId: string
    ) {
        return this.certifiedCorrectionsService.findByCertifiedDataId(
            certifiedDataId
        )
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => CertifiedPensionCorrections)
    async createCertifiedPensionCorrections(
        @Args('createCertifiedCorrectionsInput')
        createCertifiedCorrectionsInput: CreateCertifiedPensionCorrectionsInput,
        @Args('certifiedDataId') certifiedDataId: string,
        @Context() context: any
    ) {
        return this.certifiedCorrectionsService.create(
            createCertifiedCorrectionsInput,
            certifiedDataId
        )
    }
}
