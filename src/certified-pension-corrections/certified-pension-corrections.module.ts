import { Module } from '@nestjs/common'
import { CertifiedPensionCorrectionsService } from './certified-pension-corrections.service'
import { CertifiedPensionCorrectionsResolver } from './certified-pension-corrections.resolver'
import { AuthModule } from '../auth/auth.module'

@Module({
    imports: [AuthModule],
    providers: [
        CertifiedPensionCorrectionsResolver,
        CertifiedPensionCorrectionsService,
    ],
    exports: [CertifiedPensionCorrectionsService],
})
export class CertifiedPensionCorrectionsModule {}
