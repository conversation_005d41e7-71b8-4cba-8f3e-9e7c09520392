import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CertifiedPensionCorrections } from '../certified-data/entities/certified-pension-corrections.entity'
import { CreateCertifiedPensionCorrectionsInput } from '../certified-data/dto/create-certified-pension-corrections.input'

@Injectable()
export class CertifiedPensionCorrectionsService {
    constructor(private readonly prisma: PrismaService) { }

    async findOne(id: string) {
        const certifiedCorrections =
            await this.prisma.certifiedPensionCorrections.findUnique({
                where: { id },
                include: {
                    certifiedData: true,
                },
            })

        if (!certifiedCorrections) {
            throw new NotFoundException(
                `CertifiedPensionCorrections with ID ${id} not found`
            )
        }

        return certifiedCorrections
    }

    async findByCertifiedDataId(certifiedDataId: string) {
        const certifiedCorrections =
            await this.prisma.certifiedPensionCorrections.findUnique({
                where: { certifiedDataId },
                include: {
                    certifiedData: true,
                },
            })

        if (!certifiedCorrections) {
            throw new NotFoundException(
                `CertifiedPensionCorrections for certifiedDataId ${certifiedDataId} not found`
            )
        }

        return certifiedCorrections
    }

    async create(
        createCertifiedPensionCorrectionsInput: CreateCertifiedPensionCorrectionsInput,
        certifiedDataId: string
    ) {
        // Extract certificationRejectReason to handle separately
        const { certificationRejectReason, ...correctionsData } = createCertifiedPensionCorrectionsInput

        return this.prisma.certifiedPensionCorrections.create({
            data: {
                ...correctionsData,
                certifiedData: {
                    connect: { id: certifiedDataId },
                },
            },
            include: {
                certifiedData: true,
            },
        })
    }

    async changeUpdate(changes: any) {
        const { entityId, newValue, path } = changes

        // Convert to number if the value is numeric
        const processedValue = !isNaN(Number(newValue)) ? Number(newValue) : newValue

        const updateData = {
            [path]: processedValue,
        }

        const updatedInfo = await this.prisma.certifiedPensionCorrections.update({
            where: { id: entityId },
            data: updateData,
            include: {
                certifiedData: true,
            },
        })

        if (!updatedInfo) {
            throw new NotFoundException(`CertifiedPensionCorrections with ID ${entityId} not updated`)
        }

        return updatedInfo
    }

    async updatePendingChanges(id: string, changes: string[]) {
        const certifiedInfo = await this.prisma.certifiedPensionCorrections.findUnique({
            where: { id },
        })

        if (!certifiedInfo) {
            throw new NotFoundException(`CertifiedPensionCorrections with ID ${id} not found`)
        }

        const currentChanges = certifiedInfo.pendingChanges || []
        const uniqueChanges = [...new Set([...currentChanges, ...changes])]

        const updatedInfo = await this.prisma.certifiedPensionCorrections.update({
            where: { id },
            data: {
                pendingChanges: uniqueChanges,
            },
        })

        return updatedInfo
    }

    async clearPendingChanges(id: string, paths: string | string[]) {
        const pathsArray = Array.isArray(paths) ? paths : [paths]

        const toUpdate = await this.prisma.certifiedPensionCorrections.findUnique({
            where: { id },
        })

        if (!toUpdate) {
            throw new NotFoundException(`CertifiedPensionCorrections with ID ${id} not found`)
        }

        const currentChanges = toUpdate.pendingChanges || []
        const updatedChanges = currentChanges.filter(
            (path) => !pathsArray.includes(path)
        )

        return this.prisma.certifiedPensionCorrections.update({
            where: { id },
            data: { pendingChanges: updatedChanges },
        })
    }
}
