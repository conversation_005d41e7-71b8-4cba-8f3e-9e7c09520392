import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { DocumentService } from './documents.service'
import { DocumentResolver } from './documents.resolver'
import { ParticipantModule } from '../participant/participant.module'
import { AuthModule } from '../auth/auth.module'
import { AuditLogModule } from '../audit-log/audit-log.module'

@Module({
    imports: [ConfigModule, AuthModule, AuditLogModule, ParticipantModule],
    providers: [DocumentService, DocumentResolver],
    exports: [DocumentService],
})
export class DocumentsModule {}
