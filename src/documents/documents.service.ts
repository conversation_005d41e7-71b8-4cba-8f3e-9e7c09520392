import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateDocumentInput } from './dto/create-document.input'
import { UpdateDocumentInput } from './dto/update-document.input'
import { AuditLogService } from '../audit-log/audit-log.service'
import { FindAllDocumentsInput } from './dto/all-documents.input'

@Injectable()
export class DocumentService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly auditLogService: AuditLogService
    ) {}

    async create(createDocumentInput: CreateDocumentInput, userId: string) {
        // Check if participant exists
        const participant = await this.prisma.participant.findUnique({
            where: { id: createDocumentInput.participantId },
        })

        if (!participant) {
            throw new NotFoundException(
                `Participant with ID "${createDocumentInput.participantId}" not found`
            )
        }

        // Set uploadedAt if not provided
        if (!createDocumentInput.uploadedAt) {
            createDocumentInput.uploadedAt = new Date()
        }

        // Set createdBy if not provided
        if (!createDocumentInput.createdBy) {
            const user = await this.prisma.user.findUnique({
                where: { id: userId },
                select: { email: true },
            })
            createDocumentInput.createdBy = user?.email || userId
        }

        const document = await this.prisma.document.create({
            data: createDocumentInput,
            include: {
                participant: true,
            },
        })

        await this.auditLogService.create({
            action: 'CREATE',
            userId,
            entityId: document.id,
            entityType: 'DOCUMENT',
            changes: createDocumentInput,
        })

        return document
    }

    async findAll(findAllDocumentsInput: FindAllDocumentsInput) {
        const { participantId, type, searchTerm, skip, take } =
            findAllDocumentsInput

        const where: any = {}

        if (participantId) {
            where.participantId = participantId
        }

        if (type) {
            where.type = type
        }

        if (searchTerm) {
            where.OR = [
                { name: { contains: searchTerm, mode: 'insensitive' } },
                { documentId: { contains: searchTerm, mode: 'insensitive' } },
            ]
        }

        const documents = await this.prisma.document.findMany({
            where,
            skip: skip || 0,
            take: take || 50,
            orderBy: {
                uploadedAt: 'desc',
            },
            include: {
                participant: true,
            },
        })

        const totalCount = await this.prisma.document.count({ where })

        return {
            items: documents,
            totalCount,
        }
    }

    async findOne(id: string) {
        const document = await this.prisma.document.findUnique({
            where: { id },
            include: {
                participant: true,
            },
        })

        if (!document) {
            throw new NotFoundException(`Document with ID "${id}" not found`)
        }

        return document
    }

    async update(
        id: string,
        updateDocumentInput: UpdateDocumentInput,
        userId: string
    ) {
        await this.findOne(id)

        // Get original document data for audit log
        const originalDocument = await this.prisma.document.findUnique({
            where: { id },
            select: {
                name: true,
                type: true,
                path: true,
                size: true,
            },
        })

        const document = await this.prisma.document.update({
            where: { id },
            data: updateDocumentInput,
            include: {
                participant: true,
            },
        })

        await this.auditLogService.create({
            action: 'UPDATE',
            userId,
            entityId: document.id,
            entityType: 'DOCUMENT',
            changes: {
                oldValue: originalDocument,
                newValue: {
                    name: document.name,
                    type: document.type,
                    path: document.path,
                    size: document.size,
                },
            },
        })

        return document
    }

    async remove(id: string, userId: string) {
        const document = await this.findOne(id)

        await this.prisma.document.delete({
            where: { id },
        })

        await this.auditLogService.create({
            action: 'DELETE',
            userId,
            entityId: id,
            entityType: 'DOCUMENT',
            changes: {
                oldValue: {
                    id: document.id,
                    name: document.name,
                    type: document.type,
                    participantId: document.participantId,
                },
                newValue: null,
            },
        })

        return { id, deleted: true }
    }

    // Find all documents for a specific participant
    async findDocumentsByParticipant(participantId: string) {
        return this.prisma.document.findMany({
            where: {
                participantId,
            },
            orderBy: {
                uploadedAt: 'desc',
            },
        })
    }
}
