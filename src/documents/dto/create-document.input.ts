import { InputType, Field, Int } from '@nestjs/graphql'
import {
    IsDateString,
    IsInt,
    IsOptional,
    IsString,
    IsUUID,
    Min,
} from 'class-validator'

@InputType()
export class CreateDocumentInput {
    @Field()
    @IsString()
    participantId: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    documentId?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    name?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    type?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    mimeType?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    path?: string

    @Field(() => Int, { nullable: true })
    @IsInt()
    @Min(0)
    @IsOptional()
    size?: number

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    createdBy?: string

    @Field({ nullable: true })
    @IsDateString()
    @IsOptional()
    uploadedAt?: Date
}
