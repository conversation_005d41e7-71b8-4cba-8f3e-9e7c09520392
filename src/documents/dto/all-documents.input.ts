import { InputType, Field, ID, PartialType, Int } from '@nestjs/graphql'
import {
    IsDateString,
    IsInt,
    IsOptional,
    IsString,
    IsUUID,
    Min,
} from 'class-validator'

@InputType()
export class FindAllDocumentsInput {
    @Field({ nullable: true })
    @IsUUID('4')
    @IsOptional()
    participantId?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    type?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    searchTerm?: string

    @Field({ nullable: true })
    @IsOptional()
    skip?: number

    @Field({ nullable: true })
    @IsOptional()
    take?: number
}
