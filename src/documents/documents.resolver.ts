import {
    Resolver,
    Query,
    Mutation,
    Args,
    Context,
    ResolveField,
    Parent,
} from '@nestjs/graphql'
import { UseGuards } from '@nestjs/common'
import { Document } from './entities/document.entity'
import { CreateDocumentInput } from './dto/create-document.input'
import { UpdateDocumentInput } from './dto/update-document.input'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'
import { ParticipantService } from '../participant/participant.service'
import { Participant } from '../participant/entities/participant.entity'
import { DocumentService } from './documents.service'
import { FindAllDocumentsInput } from './dto/all-documents.input'

@Resolver(() => Document)
export class DocumentResolver {
    constructor(
        private readonly documentService: DocumentService,
        private readonly participantService: ParticipantService
    ) {}

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => Document)
    async createDocument(
        @Args('createDocumentInput') createDocumentInput: CreateDocumentInput,
        @Context() context: any
    ) {
        const userId = context.req.user.id
        return this.documentService.create(createDocumentInput, userId)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => [Document], { name: 'documents' })
    async findAll(
        @Args('findAllDocumentsInput', { nullable: true })
        findAllDocumentsInput: FindAllDocumentsInput = {}
    ) {
        return this.documentService.findAll(findAllDocumentsInput)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => Document, { name: 'document' })
    async findOne(@Args('id', { type: () => String }) id: string) {
        return this.documentService.findOne(id)
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => Document)
    async updateDocument(
        @Args('updateDocumentInput') updateDocumentInput: UpdateDocumentInput,
        @Context() context: any
    ) {
        const userId = context.req.user.id
        return this.documentService.update(
            updateDocumentInput.id,
            updateDocumentInput,
            userId
        )
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => Document)
    async removeDocument(
        @Args('id', { type: () => String }) id: string,
        @Context() context: any
    ) {
        const userId = context.req.user.id
        return this.documentService.remove(id, userId)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => [Document], { name: 'participantDocuments' })
    async findDocumentsByParticipant(
        @Args('participantId', { type: () => String }) participantId: string
    ) {
        return this.documentService.findDocumentsByParticipant(participantId)
    }
}
