import { InputType, Field } from '@nestjs/graphql'
import { IsEmail, IsOptional, IsString } from 'class-validator'

@InputType()
export class ResetPasswordInput {
    @IsString()
    @IsEmail()
    @Field()
    email: string

    @IsString()
    @Field()
    resetLink: string
}

@InputType()
export class NewUserInput {
    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    name?: string

    @IsString()
    @IsEmail()
    @Field()
    email: string

    @IsString()
    @Field()
    resetLink: string
}
