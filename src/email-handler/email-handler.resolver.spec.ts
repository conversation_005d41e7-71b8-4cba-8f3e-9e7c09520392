import { Test, TestingModule } from '@nestjs/testing';
import { EmailHandlerResolver } from './email-handler.resolver';
import { EmailHandlerService } from './email-handler.service';

describe('EmailHandlerResolver', () => {
  let resolver: EmailHandlerResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [EmailHandlerResolver, EmailHandlerService],
    }).compile();

    resolver = module.get<EmailHandlerResolver>(EmailHandlerResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
