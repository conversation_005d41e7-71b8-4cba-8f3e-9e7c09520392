import { Resolver, Query, Args } from '@nestjs/graphql'
import { NewUserInput, ResetPasswordInput } from './dto/welcome-email.input'
import { EmailHandler } from './entities/email-handler.entity'
import { EmailHandlerService } from './email-handler.service'

@Resolver(() => EmailHandler)
export class EmailHandlerResolver {
    constructor(private readonly emailHandlerService: EmailHandlerService) {}

    @Query(() => String, { name: 'sendRestPasswordEmail' })
    sendResetPasswordEmail(
        @Args('data', { type: () => ResetPasswordInput })
        data: ResetPasswordInput
    ) {
        return this.emailHandlerService.sendResetPasswordEmail(data)
    }

    @Query(() => String, { name: 'sendNewUserEmail' })
    sendNewUserEmail(
        @Args('data', { type: () => NewUserInput })
        data: NewUserInput
    ) {
        return this.emailHandlerService.sendNewUserEmail(data)
    }
}
