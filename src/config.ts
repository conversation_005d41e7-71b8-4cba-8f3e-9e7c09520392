import * as dotenv from 'dotenv'
import { join } from 'path'

// Load environment variables from the appropriate .env file
const envFilePath =
    process.env.NODE_ENV === 'production'
        ? join(process.cwd(), '.env.production')
        : join(process.cwd(), '.env.development')

dotenv.config({ path: envFilePath })

// Validate required environment variables
const REQUIRED_ENV_VARS = [
    'DATABASE_URL',
    'FIREBASE_PROJECT_ID',
    'FIREBASE_CLIENT_EMAIL',
    'FIREBASE_PRIVATE_KEY',
]

for (const envVar of REQUIRED_ENV_VARS) {
    if (!process.env[envVar]) {
        throw new Error(`Missing required environment variable: ${envVar}`)
    }
}

const configs = () => ({
    NODE_ENV: process.env.NODE_ENV || 'development',
    DATABASE: {
        URL: process.env.DATABASE_URL,
    },
    FIREBASE: {
        PROJECT_ID: process.env.FIREBASE_PROJECT_ID,
        CLIENT_EMAIL: process.env.FIREBASE_CLIENT_EMAIL,
        PRIVATE_KEY_ID: process.env.FIREBASE_PRIVATE_KEY_ID,
        // Properly decode the Firebase private key from Base64 if needed
        PRIVATE_KEY: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    },
    SERVER: {
        PORT: parseInt(process.env.PORT || '3500', 10),
        HOST: process.env.HOST || 'http://localhost:3500',
    },
    CLIENT: {
        HOST:
            process.env.NODE_ENV === 'production'
                ? process.env.FRONT_HOST_PROD
                : process.env.FRONT_HOST_DEV,
    },
    MAIL: {
        HOST: process.env.MAIL_HOST,
        PORT: parseInt(process.env.MAIL_PORT || '587', 10),
        USER: process.env.MAIL_USER,
        PASS: process.env.MAIL_PASS,
        FROM: process.env.MAIL_FROM,
    },
})

export default configs
