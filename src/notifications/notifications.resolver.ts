import {
    Resolver,
    Query,
    Mutation,
    Args,
    Context,
    Int,
    Subscription,
} from '@nestjs/graphql'
import { NotificationService } from './notifications.service'
import { Notification } from './entities/notification.entity'
import { CreateNotificationInput } from './dto/create-notification.input'
import { UpdateNotificationInput } from './dto/update-notification.input'
import { UseGuards, Inject } from '@nestjs/common'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'
import { RedisPubSub } from 'graphql-redis-subscriptions'

@Resolver(() => Notification)
export class NotificationResolver {
    constructor(
        private readonly notificationService: NotificationService,
        @Inject('PUB_SUB') private pubSub: RedisPubSub
    ) {}

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => Notification)
    async createNotification(
        @Args('createNotificationInput')
        createNotificationInput: CreateNotificationInput,
        @Context() context: any
    ) {
        const userId = context.req.user.id

        const notification = await this.notificationService.create(
            createNotificationInput,
            userId
        )

        // Publish notification event to recipient
        await this.pubSub.publish(
            `userNotifications.${notification.recipientId}`,
            {
                notification,
            }
        )

        return notification
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => [Notification], { name: 'getAllNotifications' })
    findAll(@Context() context: any) {
        const userId = context.req.user.id
        return this.notificationService.findAll(userId)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => [Notification], { name: 'getNotificationsByRecipient' })
    receipientNotifications(
        @Context() context: any,
        @Args('recipientId', { type: () => String }) recipientId: string
    ) {
        const userId = context.req.user.id || recipientId
        return this.notificationService.findAll(userId)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => [Notification], { name: 'unreadNotifications' })
    findUnread(@Context() context: any) {
        const userId = context.req.user.id
        return this.notificationService.findUnread(userId)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => Int, { name: 'unreadNotificationCount' })
    countUnread(@Context() context: any) {
        const userId = context.req.user.id
        return this.notificationService.countUnread(userId)
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => Notification)
    updateNotification(
        @Args('updateNotificationInput')
        updateNotificationInput: UpdateNotificationInput,
        @Context() context: any
    ) {
        const userId = context.req.user.id
        return this.notificationService.update(
            updateNotificationInput.id,
            updateNotificationInput,
            userId
        )
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => Notification)
    markNotificationAsRead(
        @Args('id', { type: () => String }) id: string,
        @Context() context: any
    ) {
        const userId = context.req.user.id
        return this.notificationService.markAsRead(id, userId)
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => Boolean)
    async markAllNotificationsAsRead(
        @Context() context: any,
        @Args('recipientId', { type: () => String }) recipientId: string
    ) {
        const userId = context.req.user.id || recipientId
        await this.notificationService.markAllAsRead(userId)
        return true
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => Notification)
    removeNotification(
        @Args('id', { type: () => String }) id: string,
        @Context() context: any
    ) {
        const userId = context.req.user.id
        return this.notificationService.remove(id, userId)
    }

    @Subscription(() => Notification, {
        filter: (payload, variables, context) => {
            const userId =
                context.req?.user?.id ||
                context.user?.id ||
                context.connection?.context?.user?.id
            return payload.notification.recipientId === userId
        },
        resolve: (value) => value.notification,
    })
    userNotifications(@Context() context: any) {
        const userId =
            context.req?.user?.id ||
            context.user?.id ||
            context.connection?.context?.user?.id
        return this.pubSub.asyncIterator(`userNotifications.${userId}`)
    }
}
