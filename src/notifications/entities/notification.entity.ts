import { ObjectType, Field, ID } from '@nestjs/graphql'
import { User } from '../../user/entities/user.entity'

@ObjectType()
export class Notification {
    @Field(() => ID)
    id: string

    @Field()
    createdAt: Date

    @Field(() => User)
    createdBy: User

    @Field()
    createdById: string

    @Field()
    message: string

    @Field(() => User)
    recipient: User

    @Field()
    recipientId: string

    @Field()
    read: boolean

    @Field({ nullable: true })
    readAt?: Date

    @Field()
    type: string

    @Field()
    entityId: string

    @Field()
    entityType: string
}
