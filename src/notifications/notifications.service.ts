import {
    Injectable,
    NotFoundException,
    ForbiddenException,
    Inject,
} from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateNotificationInput } from './dto/create-notification.input'
import { UpdateNotificationInput } from './dto/update-notification.input'

@Injectable()
export class NotificationService {
    constructor(private readonly prisma: PrismaService) {}

    private readonly notificationInclude = {
        createdBy: true,
        recipient: true,
    }

    async create(
        createNotificationInput: CreateNotificationInput,
        currentUserId: string
    ) {
        const { recipientId, message, type, entityId, entityType } =
            createNotificationInput

        const recipientExists = await this.prisma.user.findUnique({
            where: { id: recipientId },
        })

        if (!recipientExists) {
            throw new NotFoundException(
                `Recipient with id ${recipientId} not found`
            )
        }

        return this.prisma.notification.create({
            data: {
                message,
                type,
                entityId,
                entityType,
                createdBy: {
                    connect: { id: currentUserId },
                },
                recipient: {
                    connect: { id: recipientId },
                },
            },
            include: this.notificationInclude,
        })
    }

    async findAll(userId: string) {
        return this.prisma.notification.findMany({
            where: {
                recipientId: userId,
            },
            include: this.notificationInclude,
            orderBy: {
                createdAt: 'desc',
            },
        })
    }

    async findUnread(userId: string) {
        return this.prisma.notification.findMany({
            where: {
                recipientId: userId,
                read: false,
            },
            include: this.notificationInclude,
            orderBy: {
                createdAt: 'desc',
            },
        })
    }

    async findOne(id: string, userId: string) {
        const notification = await this.prisma.notification.findUnique({
            where: { id },
            include: this.notificationInclude,
        })

        if (!notification) {
            throw new NotFoundException(`Notification with id ${id} not found`)
        }

        // if (notification.recipientId !== userId) {
        //     throw new ForbiddenException(
        //         'You do not have permission to access this notification'
        //     )
        // }

        return notification
    }

    async update(
        id: string,
        updateNotificationInput: UpdateNotificationInput,
        userId: string
    ) {
        await this.findOne(id, userId)

        const updateData: any = { ...updateNotificationInput }
        delete updateData.id

        if (updateData.read === true) {
            updateData.readAt = new Date()
        }

        return this.prisma.notification.update({
            where: { id },
            data: updateData,
            include: this.notificationInclude,
        })
    }

    async markAsRead(id: string, userId: string) {
        await this.findOne(id, userId)

        return this.prisma.notification.update({
            where: { id },
            data: {
                read: true,
                readAt: new Date(),
            },
            include: this.notificationInclude,
        })
    }

    async markAllAsRead(userId: string) {
        return this.prisma.notification.updateMany({
            where: {
                recipientId: userId,
                read: false,
            },
            data: {
                read: true,
                readAt: new Date(),
            },
        })
    }

    async remove(id: string, userId: string) {
        // First check if the notification exists and belongs to the user
        await this.findOne(id, userId)

        return this.prisma.notification.delete({
            where: { id },
            include: this.notificationInclude,
        })
    }

    async countUnread(userId: string) {
        return this.prisma.notification.count({
            where: {
                recipientId: userId,
                read: false,
            },
        })
    }
}
