import { Module } from '@nestjs/common'
import { NotificationResolver } from './notifications.resolver'
import { NotificationService } from './notifications.service'
import { PubSubModule } from '../pub-sub/pub-sub.module'
import { AuthModule } from '../auth/auth.module'

@Module({
    imports: [PubSubModule, AuthModule],
    exports: [NotificationService],
    providers: [NotificationResolver, NotificationService],
})
export class NotificationsModule {}
