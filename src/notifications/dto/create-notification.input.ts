import { InputType, Field } from '@nestjs/graphql'
import { IsNotEmpty, IsString, IsUUID } from 'class-validator'

@InputType()
export class CreateNotificationInput {
    @Field()
    @IsNotEmpty()
    @IsString()
    message: string

    @Field()
    @IsNotEmpty()
    @IsUUID()
    recipientId: string

    @Field()
    @IsNotEmpty()
    @IsString()
    type: string

    @Field()
    @IsNotEmpty()
    @IsString()
    entityId: string

    @Field()
    @IsNotEmpty()
    @IsString()
    entityType: string
}
