import { CreateNotificationInput } from './create-notification.input'
import { InputType, Field, PartialType, ID } from '@nestjs/graphql'
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator'

@InputType()
export class UpdateNotificationInput extends PartialType(
    CreateNotificationInput
) {
    @Field(() => ID)
    @IsNotEmpty()
    @IsString()
    id: string

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    read?: boolean
}
