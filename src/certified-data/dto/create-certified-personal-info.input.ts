import { InputType, Field, Int } from '@nestjs/graphql'
import {
    IsInt,
    IsOptional,
    IsString,
    IsJSON,
    ValidateNested,
} from 'class-validator'
import { GraphQLJSON } from 'graphql-type-json'
import { Type } from 'class-transformer'
import { CreateCertificationRejectReasonInput } from '../../certification-reject-reason/dto/create-certification-reject-reason.input'
import { CreateCertifiedPartnerInfoInput } from './create-certified-partner-info.input'

@InputType()
export class CreateCertifiedPersonalInfoInput {
    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    firstName?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    lastName?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    email?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    phone?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    maritalStatus?: string

    @Field(() => Int, { nullable: true })
    @IsInt()
    @IsOptional()
    birthDay?: number

    @Field(() => Int, { nullable: true })
    @IsInt()
    @IsOptional()
    birthMonth?: number

    @Field(() => Int, { nullable: true })
    @IsInt()
    @IsOptional()
    birthYear?: number

    @Field(() => GraphQLJSON, { nullable: true })
    @IsJSON()
    @IsOptional()
    address?: any

    @Field(() => GraphQLJSON, { nullable: true })
    @IsJSON()
    @IsOptional()
    partnerInfo?: any

    @Field(() => GraphQLJSON, { nullable: true })
    @IsJSON()
    @IsOptional()
    children?: any

    @Field(() => [CreateCertificationRejectReasonInput], { nullable: true })
    @ValidateNested({ each: true })
    @Type(() => CreateCertificationRejectReasonInput)
    @IsOptional()
    certificationRejectReason?: CreateCertificationRejectReasonInput[]
}
