import { InputType, Field, ID, Int } from '@nestjs/graphql'
import { IsInt, IsOptional, IsString } from 'class-validator'

@InputType()
export class FindOneCertifiedDataInput {
    @Field(() => ID, { nullable: true })
    @IsString()
    @IsOptional()
    id?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    participantId?: string

    @Field(() => Int, { nullable: true })
    @IsInt()
    @IsOptional()
    certificationYear?: number
}
