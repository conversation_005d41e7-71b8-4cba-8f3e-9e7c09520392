import { InputType, Field } from '@nestjs/graphql'
import { IsJSON, IsOptional, ValidateNested } from 'class-validator'
import { GraphQLJSON } from 'graphql-type-json'
import { Type } from 'class-transformer'
import { CreateCertificationRejectReasonInput } from '../../certification-reject-reason/dto/create-certification-reject-reason.input'

@InputType()
export class CreateCertifiedVoluntaryContributionsInput {
    @Field(() => GraphQLJSON, { nullable: true })
    @IsJSON()
    @IsOptional()
    contributions?: any

    @Field(() => [CreateCertificationRejectReasonInput], { nullable: true })
    @ValidateNested({ each: true })
    @Type(() => CreateCertificationRejectReasonInput)
    @IsOptional()
    certificationRejectReason?: CreateCertificationRejectReasonInput[]
} 