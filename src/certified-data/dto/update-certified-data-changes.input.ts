import { InputType, Field } from '@nestjs/graphql'
import { IsString, IsArray, IsOptional, IsUUID, ArrayNotEmpty } from 'class-validator'

@InputType()
export class UpdateCertifiedDataApprovedChangesInput {
    @Field()
    @IsString()
    @IsUUID('4')
    id: string

    @Field(() => [String])
    @IsArray()
    @ArrayNotEmpty()
    @IsString({ each: true })
    approvedChanges: string[]

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    entityType?: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    @IsUUID('4')
    entityId?: string
}

@InputType()
export class UpdateCertifiedDataRejectedChangesInput {
    @Field()
    @IsString()
    @IsUUID('4')
    id: string

    @Field(() => [String])
    @IsArray()
    @ArrayNotEmpty()
    @IsString({ each: true })
    rejectedChanges: string[]

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    entityType?: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    rejectReason?: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    @IsUUID('4')
    entityId?: string
}

@InputType()
export class UpdateCertificationStatusInput {
    @Field()
    @IsString()
    @IsUUID('4')
    id: string

    @Field()
    @IsString()
    status: string
}
