import { Field, InputType, Int } from '@nestjs/graphql'
import { IsInt, IsOptional, IsString, IsUUID } from 'class-validator'

@InputType()
export class FindAllCertifiedDataInput {
    @Field({ nullable: true })
    @IsUUID('4')
    @IsOptional()
    participantId?: string

    @Field(() => Int, { nullable: true })
    @IsInt()
    @IsOptional()
    certificationYear?: number

    @Field(() => Int, { nullable: true })
    @IsInt()
    @IsOptional()
    skip?: number

    @Field(() => Int, { nullable: true })
    @IsInt()
    @IsOptional()
    take?: number

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    sortBy?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    sortOrder?: 'asc' | 'desc'
}
