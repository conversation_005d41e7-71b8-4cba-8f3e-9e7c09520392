import { InputType, Field, Int } from '@nestjs/graphql'
import { IsInt, IsBoolean, Min, Max } from 'class-validator'

@InputType()
export class RevertCertificationChangesInput {
    @Field(() => Int)
    @IsInt()
    @Min(1900)
    @Max(2100)
    certificationYear: number
}

@InputType()
export class RevertCertificationChangesWithConfirmationInput {
    @Field(() => Int)
    @IsInt()
    @Min(1900)
    @Max(2100)
    certificationYear: number

    @Field(() => Boolean)
    @IsBoolean()
    confirmRevert: boolean
}

@InputType()
export class PreviewRevertChangesInput {
    @Field(() => Int)
    @IsInt()
    @Min(1900)
    @Max(2100)
    certificationYear: number
}
