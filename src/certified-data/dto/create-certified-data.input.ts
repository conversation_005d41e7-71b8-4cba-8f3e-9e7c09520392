import { InputType, Field, Int, GraphQLISODateTime } from '@nestjs/graphql'
import {
    IsDate,
    IsInt,
    IsJSON,
    IsOptional,
    IsString,
    IsUUID,
    Max,
    Min,
    ValidateNested,
} from 'class-validator'
import { GraphQLJSON } from 'graphql-type-json'
import { Type } from 'class-transformer'
import { CreateCertifiedPensionInfoInput } from './create-certified-pension-info.input'
import { CreateCertifiedEmploymentInfoInput } from './create-certified-employment-info.input'
import { CreateCertifiedPersonalInfoInput } from './create-certified-personal-info.input'
import { CreateCertifiedIndexationStartOfYearInput } from './create-certified-indexation-start-of-year.input'
import { CreateCertifiedPensionCorrectionsInput } from './create-certified-pension-corrections.input'
import { CreateCertifiedVoluntaryContributionsInput } from './create-certified-voluntary-contributions.input'
import { CreateCertifiedPensionParametersInput } from './create-certified-pension-parameters.input'
import { CreateCertificationRejectReasonInput } from '../../certification-reject-reason/dto/create-certification-reject-reason.input'

@InputType()
export class ParticipantCreateNestedOneWithoutCertifiedDataInput {
    @Field()
    @IsUUID('4')
    connect: string
}

@InputType()
export class UserCreateNestedOneWithoutCertifiedDataInput {
    @Field()
    @IsUUID('4')
    connect: string
}

@InputType()
export class CreateCertifiedDataInput {
    @Field({ nullable: true })
    @IsUUID('4')
    @IsOptional()
    id?: string

    @Field(() => Int)
    @IsInt()
    @Min(1900)
    @Max(2100)
    certificationYear: number

    @Field(() => GraphQLISODateTime)
    @IsDate()
    @Type(() => Date)
    certifiedAt: Date

    @Field(() => CreateCertifiedPensionInfoInput, { nullable: true })
    @ValidateNested()
    @Type(() => CreateCertifiedPensionInfoInput)
    @IsOptional()
    certifiedPensionInfo?: CreateCertifiedPensionInfoInput

    @Field(() => CreateCertifiedEmploymentInfoInput, { nullable: true })
    @ValidateNested()
    @Type(() => CreateCertifiedEmploymentInfoInput)
    @IsOptional()
    certifiedEmploymentInfo?: CreateCertifiedEmploymentInfoInput

    @Field(() => CreateCertifiedPersonalInfoInput, { nullable: true })
    @ValidateNested()
    @Type(() => CreateCertifiedPersonalInfoInput)
    @IsOptional()
    certifiedPersonalInfo?: CreateCertifiedPersonalInfoInput

    @Field(() => CreateCertifiedIndexationStartOfYearInput, { nullable: true })
    @ValidateNested()
    @Type(() => CreateCertifiedIndexationStartOfYearInput)
    @IsOptional()
    certifiedIndexationStartOfYear?: CreateCertifiedIndexationStartOfYearInput

    @Field(() => CreateCertifiedPensionCorrectionsInput, { nullable: true })
    @ValidateNested()
    @Type(() => CreateCertifiedPensionCorrectionsInput)
    @IsOptional()
    certifiedPensionCorrections?: CreateCertifiedPensionCorrectionsInput

    @Field(() => CreateCertifiedVoluntaryContributionsInput, { nullable: true })
    @ValidateNested()
    @Type(() => CreateCertifiedVoluntaryContributionsInput)
    @IsOptional()
    certifiedVoluntaryContributions?: CreateCertifiedVoluntaryContributionsInput

    @Field(() => CreateCertifiedPensionParametersInput, { nullable: true })
    @ValidateNested()
    @Type(() => CreateCertifiedPensionParametersInput)
    @IsOptional()
    certifiedPensionParameters?: CreateCertifiedPensionParametersInput

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    notes?: string

    @Field(() => [CreateCertificationRejectReasonInput], { nullable: true })
    @ValidateNested({ each: true })
    @Type(() => CreateCertificationRejectReasonInput)
    @IsOptional()
    certificationRejectReason?: CreateCertificationRejectReasonInput[]

    @Field(() => ParticipantCreateNestedOneWithoutCertifiedDataInput)
    participant: ParticipantCreateNestedOneWithoutCertifiedDataInput

    @Field(() => UserCreateNestedOneWithoutCertifiedDataInput)
    certifiedBy: UserCreateNestedOneWithoutCertifiedDataInput
}
