import { Field, InputType, ObjectType } from '@nestjs/graphql'
import { IsString, IsUUID } from 'class-validator'

@InputType()
export class RevertSingleFieldInput {
    @Field()
    @IsString()
    @IsUUID('4')
    entityId: string

    @Field()
    @IsString()
    entityType: string

    @Field()
    @IsString()
    path: string
}

@ObjectType()
export class RevertSingleFieldResponse {
    @Field()
    success: boolean

    @Field()
    entityId: string

    @Field()
    entityType: string

    @Field()
    path: string

    @Field()
    message: string
} 