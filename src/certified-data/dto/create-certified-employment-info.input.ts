import { InputType, Field, Int, GraphQLISODateTime } from '@nestjs/graphql'
import {
    IsInt,
    IsOptional,
    IsString,
    IsDate,
    IsJSON,
    ValidateNested
} from 'class-validator'
import { Type } from 'class-transformer'
import { GraphQLJSON } from 'graphql-type-json'
import { CreateCertificationRejectReasonInput } from '../../certification-reject-reason/dto/create-certification-reject-reason.input'

@InputType()
export class CreateCertifiedEmploymentInfoInput {
    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    employeeId?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    department?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    position?: string

    @Field(() => Int, { nullable: true })
    @IsInt()
    @IsOptional()
    regNum?: number

    @Field(() => Int, { nullable: true })
    @IsInt()
    @IsOptional()
    havNum?: number

    @Field(() => GraphQLISODateTime, { nullable: true })
    @IsDate()
    @Type(() => Date)
    @IsOptional()
    startDate?: Date

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    status?: string

    @Field(() => GraphQLJSON, { nullable: true })
    @IsJSON()
    @IsOptional()
    salaryEntries?: any

    @Field(() => [CreateCertificationRejectReasonInput], { nullable: true })
    @ValidateNested({ each: true })
    @Type(() => CreateCertificationRejectReasonInput)
    @IsOptional()
    certificationRejectReason?: CreateCertificationRejectReasonInput[]
} 