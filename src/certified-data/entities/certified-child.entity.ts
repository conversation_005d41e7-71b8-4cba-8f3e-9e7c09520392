import { Field, ID, ObjectType } from '@nestjs/graphql'
import { CertifiedData } from './certified-data.entity'
import { CertifiedPersonalInfo } from './certified-personal-info.entity'
import { CertificationRejectReason } from '../../certification-reject-reason/entities/certification-reject-reason.entity'

@ObjectType()
export class CertifiedChild {
    @Field(() => ID)
    id: string

    @Field(() => CertifiedData)
    certifiedData: CertifiedData

    @Field()
    certifiedDataId: string

    @Field(() => CertifiedPersonalInfo, { nullable: true })
    personalInfo?: CertifiedPersonalInfo

    @Field({ nullable: true })
    firstName?: string

    @Field({ nullable: true })
    lastName?: string

    @Field({ nullable: true })
    dateOfBirth?: Date

    @Field()
    isOrphan: boolean

    @Field()
    isStudying: boolean

    @Field(() => [String], { nullable: true })
    pendingChanges?: string[]

    @Field(() => [String], { nullable: true })
    requestedChanges?: string[]

    @Field(() => [String], { nullable: true })
    approvedChanges?: string[]

    @Field(() => [CertificationRejectReason], { nullable: true })
    certificationRejectReason?: CertificationRejectReason[]

    @Field(() => [String], { nullable: true })
    differences?: string[]
}
