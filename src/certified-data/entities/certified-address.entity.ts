import { ObjectType, Field, ID } from '@nestjs/graphql'
import { CertifiedPersonalInfo } from './certified-personal-info.entity'

@ObjectType()
export class CertifiedAddress {
    @Field(() => ID)
    id: string

    @Field(() => CertifiedPersonalInfo)
    personalInfo: CertifiedPersonalInfo

    @Field({ nullable: true })
    street?: string

    @Field({ nullable: true })
    houseNumber?: string

    @Field({ nullable: true })
    postalCode?: string

    @Field({ nullable: true })
    city?: string

    @Field({ nullable: true })
    state?: string

    @Field({ nullable: true })
    country?: string

    @Field(() => [String], { nullable: true })
    pendingChanges?: string[]

    @Field(() => [String], { nullable: true })
    requestedChanges?: string[]

    @Field(() => [String], { nullable: true })
    approvedChanges?: string[]

    @Field(() => [String], { nullable: true })
    differences?: string[]
}
