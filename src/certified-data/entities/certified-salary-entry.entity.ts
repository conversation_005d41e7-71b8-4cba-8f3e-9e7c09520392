import { ObjectType, Field } from '@nestjs/graphql'
import { CertifiedEmploymentInfo } from './certified-employment-info.entity'
import { CertificationRejectReason } from '../../certification-reject-reason/entities/certification-reject-reason.entity'

@ObjectType()
export class CertifiedSalaryEntry {
    @Field()
    id: string

    @Field(() => CertifiedEmploymentInfo)
    certifiedEmploymentInfo: CertifiedEmploymentInfo

    @Field()
    year: number

    @Field()
    amount: number

    @Field()
    partTimePercentage: number

    @Field(() => [String], { nullable: true })
    pendingChanges?: string[]

    @Field(() => [String], { nullable: true })
    requestedChanges?: string[]

    @Field(() => [String], { nullable: true })
    approvedChanges?: string[]

    @Field(() => [CertificationRejectReason], { nullable: true })
    certificationRejectReason?: CertificationRejectReason[]

    @Field(() => [String], { nullable: true })
    differences?: string[]
}
