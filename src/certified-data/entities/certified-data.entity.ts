import { ObjectType, Field, ID, Int, GraphQLISODateTime } from '@nestjs/graphql'
import { User } from '../../user/entities/user.entity'
import { Participant } from '../../participant/entities/participant.entity'
import { CertifiedPensionInfo } from './certified-pension-info.entity'
import { CertifiedEmploymentInfo } from './certified-employment-info.entity'
import { CertifiedPersonalInfo } from './certified-personal-info.entity'
import { CertifiedIndexationStartOfYear } from './certified-indexation-start-of-year.entity'
import { CertifiedPensionCorrections } from './certified-pension-corrections.entity'
import { CertifiedVoluntaryContributions } from './certified-voluntary-contributions.entity'
import { CertifiedPensionParameters } from './certified-pension-parameters.entity'
import { CertificationRejectReason } from '../../certification-reject-reason/entities/certification-reject-reason.entity'
import { CertifiedPartnerInfo } from './certified-partner-info.entity'
import { CertifiedChild } from './certified-child.entity'
import { CertifiedAddress } from './certified-address.entity'

@ObjectType()
export class CertifiedData {
    @Field(() => ID)
    id: string

    @Field()
    participantId: string

    @Field(() => Participant)
    participant: Participant

    @Field(() => Int)
    certificationYear: number

    @Field(() => GraphQLISODateTime)
    certifiedAt: Date

    @Field()
    certifiedById: string

    @Field(() => User)
    certifiedBy: User

    @Field(() => CertifiedPensionInfo, { nullable: true })
    certifiedPensionInfo?: CertifiedPensionInfo

    @Field(() => CertifiedEmploymentInfo, { nullable: true })
    certifiedEmploymentInfo?: CertifiedEmploymentInfo

    @Field(() => CertifiedPersonalInfo, { nullable: true })
    certifiedPersonalInfo?: CertifiedPersonalInfo

    @Field(() => CertifiedIndexationStartOfYear, { nullable: true })
    certifiedIndexationStartOfYear?: CertifiedIndexationStartOfYear

    @Field(() => CertifiedPensionCorrections, { nullable: true })
    certifiedPensionCorrections?: CertifiedPensionCorrections

    @Field(() => CertifiedVoluntaryContributions, { nullable: true })
    certifiedVoluntaryContributions?: CertifiedVoluntaryContributions

    @Field(() => CertifiedPensionParameters, { nullable: true })
    certifiedPensionParameters?: CertifiedPensionParameters

    @Field(() => [CertifiedChild], { nullable: true })
    certifiedChild?: CertifiedChild[]

    @Field(() => [CertifiedPartnerInfo], { nullable: true })
    certifiedPartnerInfo?: CertifiedPartnerInfo[]

    @Field(() => CertifiedAddress, { nullable: true })
    certifiedAddress?: CertifiedAddress

    @Field({ nullable: true })
    certificationStatus?: string

    @Field({ nullable: true })
    notes?: string

    @Field(() => [CertificationRejectReason], { nullable: true })
    certificationRejectReason?: CertificationRejectReason[]

    @Field(() => [String], { nullable: true })
    differences?: string[]
}
