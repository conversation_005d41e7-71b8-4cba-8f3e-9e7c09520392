import { ObjectType, Field } from '@nestjs/graphql'
import { GraphQLJSONObject } from 'graphql-type-json'
import { CertifiedData } from './certified-data.entity'

@ObjectType()
export class CertifiedDataByYearResponse {
    @Field(() => GraphQLJSONObject, {
        description:
            'Certifications grouped by year, with keys being the year strings',
    })
    data: Record<string, CertifiedData[]>
    differences: string[]
}
