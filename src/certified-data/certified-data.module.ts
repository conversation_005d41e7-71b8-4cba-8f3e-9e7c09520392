import { Module } from '@nestjs/common'
import { CertifiedDataService } from './certified-data.service'
import { CertifiedDataResolver } from './certified-data.resolver'
import { AuthModule } from '../auth/auth.module'
import { AuditLogModule } from '../audit-log/audit-log.module'
import { ParticipantModule } from '../participant/participant.module'
import { UserModule } from '../user/user.module'
import { NotificationsModule } from '../notifications/notifications.module'
import { CertificationRejectReasonModule } from '../certification-reject-reason/certification-reject-reason.module'

@Module({
    imports: [
        AuthModule,
        AuditLogModule,
        ParticipantModule,
        UserModule,
        NotificationsModule,
        CertificationRejectReasonModule,
    ],
    providers: [CertifiedDataResolver, CertifiedDataService],
    exports: [CertifiedDataService],
})
export class CertifiedDataModule {}
