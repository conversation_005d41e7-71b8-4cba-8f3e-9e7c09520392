import {
    Resolver,
    Query,
    Mutation,
    Args,
    Context,
    ResolveField,
    Parent,
    Int,
} from '@nestjs/graphql'
import { UseGuards } from '@nestjs/common'
import { AuditLogService } from './audit-log.service'
import { AuditLog } from './entities/audit-log.entity'
import { CreateAuditLogInput } from './dto/create-audit-log.input'
import { FindAllAuditLogsInput } from './dto/find-all-audit-logs.input'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'
import { User } from '../user/entities/user.entity'
import { UserService } from '../user/user.service'
import { PaginatedAuditLogs } from './entities/paginated-audit-log.entity'

@Resolver(() => AuditLog)
export class AuditLogResolver {
    constructor(
        private readonly auditLogService: AuditLogService,
        private readonly userService: UserService
    ) {}

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => AuditLog)
    async createAuditLog(
        @Args('createAuditLogInput') createAuditLogInput: CreateAuditLogInput,
        @Context() context: any
    ) {
        // Get IP address and user agent from context if available and not provided
        if (!createAuditLogInput.ipAddress && context.req?.ip) {
            createAuditLogInput.ipAddress = context.req.ip
        }

        if (
            !createAuditLogInput.userAgent &&
            context.req?.headers?.['user-agent']
        ) {
            createAuditLogInput.userAgent = context.req.headers['user-agent']
        }

        return this.auditLogService.create(createAuditLogInput)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => PaginatedAuditLogs, { name: 'auditLogs' })
    async findAll(
        @Args('findAllAuditLogsInput', { nullable: true })
        findAllAuditLogsInput: FindAllAuditLogsInput = {}
    ) {
        return this.auditLogService.findAll(findAllAuditLogsInput)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => AuditLog, { name: 'auditLog' })
    async findOne(@Args('id', { type: () => String }) id: string) {
        return this.auditLogService.findOne(id)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => [AuditLog], { name: 'entityAuditLogs' })
    async findEntityAuditLogs(
        @Args('entityId', { type: () => String }) entityId: string,
        @Args('entityType', { type: () => String }) entityType: string
    ) {
        return this.auditLogService.findAllByEntity(entityId, entityType)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => [AuditLog], { name: 'userAuditLogs' })
    async findUserAuditLogs(
        @Args('userId', { type: () => String }) userId: string
    ) {
        return this.auditLogService.findAllByUser(userId)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => [AuditLog], { name: 'timeRangeAuditLogs' })
    async findTimeRangeAuditLogs(
        @Args('startDate') startDate: Date,
        @Args('endDate') endDate: Date
    ) {
        return this.auditLogService.findAllByTimeRange(startDate, endDate)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => Int, { name: 'totalAuditLogs' })
    async countAuditLogs(
        @Args('entityType', { type: () => String, nullable: true })
        entityType?: string
    ) {
        const result = await this.auditLogService.findAll({
            entityType,
            take: 1, // Just need count, not actual items
        })
        return result.totalCount
    }

    @ResolveField('user', () => User, { nullable: true })
    async getUser(@Parent() auditLog: AuditLog) {
        if (!auditLog.userId) {
            return null
        }

        try {
            return await this.userService.findOne(auditLog.userId)
        } catch (error) {
            // If user doesn't exist, return null rather than error
            return null
        }
    }
}
