import { <PERSON>du<PERSON> } from '@nestjs/common'
import { AuditLogService } from './audit-log.service'
import { AuditLogResolver } from './audit-log.resolver'
import { PrismaService } from '../prisma.service'
import { UserService } from '../user/user.service'
import { AuthModule } from '../auth/auth.module'
import { EmailHandlerModule } from '../email-handler/email-handler.module'

@Module({
    imports: [AuthModule, EmailHandlerModule],
    providers: [AuditLogResolver, AuditLogService, PrismaService, UserService],
    exports: [AuditLogService],
})
export class AuditLogModule {}
