import { InputType, Field } from '@nestjs/graphql'
import { IsIP, IsJSON, IsOptional, IsString, IsUUID } from 'class-validator'
import { GraphQLJSON } from 'graphql-type-json'

@InputType()
export class CreateAuditLogInput {
    @Field()
    @IsString()
    action: string

    @Field({ nullable: true })
    @IsString()
    userId?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    userRole?: string

    @Field()
    @IsString()
    entityId: string

    @Field()
    @IsString()
    entityType: string

    @Field({ nullable: true })
    @IsUUID('4')
    @IsOptional()
    proposalId?: string

    @Field(() => GraphQLJSON)
    @IsOptional()
    @IsJSON()
    changes?: any

    @Field({ nullable: true })
    @IsIP()
    @IsOptional()
    ipAddress?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    userAgent?: string
}
