import { InputType, Field, GraphQLISODateTime } from '@nestjs/graphql'
import { IsDateString, IsOptional, IsString, IsUUID } from 'class-validator'

@InputType()
export class FindAllAuditLogsInput {
    @Field({ nullable: true })
    @IsUUID('4')
    @IsOptional()
    userId?: string

    @Field({ nullable: true })
    @IsUUID('4')
    @IsOptional()
    entityId?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    entityType?: string

    @Field({ nullable: true })
    @IsUUID('4')
    @IsOptional()
    proposalId?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    action?: string

    @Field(() => GraphQLISODateTime, { nullable: true })
    @IsDateString()
    @IsOptional()
    startDate?: Date

    @Field({ nullable: true })
    @IsDateString()
    @IsOptional()
    endDate?: Date

    @Field({ nullable: true })
    @IsOptional()
    skip?: number

    @Field({ nullable: true })
    @IsOptional()
    take?: number

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    sortBy?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    sortOrder?: 'asc' | 'desc'
}
