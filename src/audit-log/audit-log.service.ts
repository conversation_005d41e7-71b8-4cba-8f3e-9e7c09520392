import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateAuditLogInput } from './dto/create-audit-log.input'
import { UpdateAuditLogInput } from './dto/update-audit-log.input'
import { FindAllAuditLogsInput } from './dto/find-all-audit-logs.input'

@Injectable()
export class AuditLogService {
    constructor(private readonly prisma: PrismaService) {}

    async create(createAuditLogInput: CreateAuditLogInput) {
        if (!createAuditLogInput.userRole) {
            const user = await this.prisma.user.findUnique({
                where: { id: createAuditLogInput.userId },
                include: { role: true },
            })

            if (user && user.role) {
                createAuditLogInput.userRole = user.role.name
            }
        }

        // return this.prisma.auditLog.create({
        //     data: createAuditLogInput,
        //     include: {
        //         user: {
        //             select: {
        //                 id: true,
        //                 email: true,
        //                 firstname: true,
        //                 lastname: true,
        //                 role: {
        //                     select: {
        //                         id: true,
        //                         name: true,
        //                     },
        //                 },
        //             },
        //         },
        //     },
        // })
    }

    async findAll(findAllAuditLogsInput: FindAllAuditLogsInput) {
        const {
            userId,
            entityId,
            entityType,
            proposalId,
            action,
            startDate,
            endDate,
            skip,
            take,
            sortBy,
            sortOrder,
        } = findAllAuditLogsInput

        const where: any = {}

        if (userId) {
            where.userId = userId
        }

        if (entityId && entityType) {
            where.AND = [{ entityId: entityId }, { entityType: entityType }]
        } else if (entityId) {
            where.entityId = entityId
        } else if (entityType) {
            where.entityType = entityType
        }

        if (proposalId) {
            where.proposalId = proposalId
        }

        if (action) {
            where.action = action
        }

        if (startDate || endDate) {
            where.timestamp = {}

            if (startDate) {
                where.timestamp.gte = startDate
            }

            if (endDate) {
                where.timestamp.lte = endDate
            }
        }

        const orderBy: any = {}
        if (sortBy) {
            orderBy[sortBy] = sortOrder || 'desc'
        } else {
            orderBy.timestamp = 'desc'
        }

        const auditLogs = await this.prisma.auditLog.findMany({
            where,
            orderBy,
            skip: skip || 0,
            take: take || 50,
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstname: true,
                        lastname: true,
                        role: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                    },
                },
            },
        })

        const totalCount = await this.prisma.auditLog.count({ where })

        return {
            items: auditLogs,
            totalCount,
        }
    }

    async findOne(id: string) {
        const auditLog = await this.prisma.auditLog.findUnique({
            where: { id },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstname: true,
                        lastname: true,
                        role: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                    },
                },
            },
        })

        if (!auditLog) {
            throw new NotFoundException(`AuditLog with ID "${id}" not found`)
        }

        return auditLog
    }

    async findAllByEntity(entityId: string, entityType: string) {
        return this.prisma.auditLog.findMany({
            where: {
                entityId,
                entityType,
            },
            orderBy: {
                timestamp: 'desc',
            },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstname: true,
                        lastname: true,
                    },
                },
            },
        })
    }

    async findAllByUser(userId: string) {
        return this.prisma.auditLog.findMany({
            where: {
                userId,
            },
            orderBy: {
                timestamp: 'desc',
            },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstname: true,
                        lastname: true,
                    },
                },
            },
        })
    }

    // Find all audit logs for a specific time period
    async findAllByTimeRange(startDate: Date, endDate: Date) {
        return this.prisma.auditLog.findMany({
            where: {
                timestamp: {
                    gte: startDate,
                    lte: endDate,
                },
            },
            orderBy: {
                timestamp: 'desc',
            },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstname: true,
                        lastname: true,
                    },
                },
            },
        })
    }
}
