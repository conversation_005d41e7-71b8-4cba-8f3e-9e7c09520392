import { ObjectType, Field } from '@nestjs/graphql'
import { User } from '../../user/entities/user.entity'
import { GraphQLJSON } from 'graphql-type-json'

@ObjectType()
export class AuditLog {
    @Field()
    id: string

    @Field()
    timestamp: Date

    @Field()
    action: string

    @Field()
    userId: string

    @Field(() => User, { nullable: true })
    user?: User

    @Field({ nullable: true })
    userRole?: string

    @Field()
    entityId: string

    @Field()
    entityType: string

    @Field({ nullable: true })
    proposalId?: string

    @Field(() => GraphQLJSON)
    changes?: JSON

    @Field({ nullable: true })
    ipAddress?: string

    @Field({ nullable: true })
    userAgent?: string
}
