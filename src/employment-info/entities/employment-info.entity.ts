import { ObjectType, Field, ID } from '@nestjs/graphql'
import { Participant } from '../../participant/entities/participant.entity'
import { SalaryEntry } from '../../salary-entry/entities/salary-entry.entity'

@ObjectType()
export class EmploymentInfo {
    @Field(() => ID)
    id: string

    @Field(() => Participant)
    participant: Participant

    @Field({ nullable: true })
    employeeId?: string

    @Field({ nullable: true })
    department?: string

    @Field({ nullable: true })
    position?: string

    @Field({ nullable: true })
    regNum?: number

    @Field({ nullable: true })
    havNum?: number

    @Field({ nullable: true })
    startDate?: Date

    @Field({ nullable: true })
    endDate?: Date

    @Field({ nullable: true })
    status?: string

    @Field(() => [SalaryEntry])
    salaryEntries: SalaryEntry[]
}
