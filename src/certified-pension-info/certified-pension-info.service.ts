import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CertifiedPensionInfo } from '../certified-data/entities/certified-pension-info.entity'
import { CreateCertifiedPensionInfoInput } from '../certified-data/dto/create-certified-pension-info.input'
import { BaseService } from '../common/base.service'

@Injectable()
export class CertifiedPensionInfoService extends BaseService {
    constructor(private readonly prisma: PrismaService) {
        super()
    }

    async findOne(id: string): Promise<CertifiedPensionInfo> {
        const certifiedPensionInfo = await this.prisma.certifiedPensionInfo.findUnique({
            where: { id },
            include: {
                certifiedData: true,
            },
        })

        if (!certifiedPensionInfo) {
            throw new NotFoundException(`CertifiedPensionInfo with ID ${id} not found`)
        }

        return certifiedPensionInfo as unknown as CertifiedPensionInfo
    }

    async findByCertifiedDataId(certifiedDataId: string): Promise<CertifiedPensionInfo> {
        const certifiedPensionInfo = await this.prisma.certifiedPensionInfo.findUnique({
            where: { certifiedDataId },
            include: {
                certifiedData: true,
            },
        })

        if (!certifiedPensionInfo) {
            throw new NotFoundException(`CertifiedPensionInfo for certifiedDataId ${certifiedDataId} not found`)
        }

        return certifiedPensionInfo as unknown as CertifiedPensionInfo
    }

    async create(
        createCertifiedPensionInfoInput: CreateCertifiedPensionInfoInput,
        certifiedDataId: string
    ): Promise<CertifiedPensionInfo> {
        // Extract certificationRejectReason to handle separately
        const { certificationRejectReason, ...pensionData } = createCertifiedPensionInfoInput

        const created = await this.prisma.certifiedPensionInfo.create({
            data: {
                ...pensionData,
                certifiedData: {
                    connect: { id: certifiedDataId },
                },
            },
            include: {
                certifiedData: true,
            },
        })

        return created as unknown as CertifiedPensionInfo
    }

    async changeUpdate(changes: any) {
        const { entityId, newValue, path } = changes

        const value = this.handleDateFields(path, newValue)

        const updateData = {
            [path]: value,
        }

        const updatedInfo = await this.prisma.certifiedPensionInfo.update({
            where: { id: entityId },
            data: updateData,
            include: {
                certifiedData: true,
            },
        })

        if (!updatedInfo) {
            throw new NotFoundException(`CertifiedPensionInfo with ID ${entityId} not updated`)
        }

        return updatedInfo as unknown as CertifiedPensionInfo
    }

    async updatePendingChanges(id: string, changes: string[]) {
        const certifiedInfo = await this.prisma.certifiedPensionInfo.findUnique({
            where: { id },
        })

        if (!certifiedInfo) {
            throw new NotFoundException(`CertifiedPensionInfo with ID ${id} not found`)
        }

        const currentChanges = certifiedInfo.pendingChanges || []
        const uniqueChanges = [...new Set([...currentChanges, ...changes])]

        const updatedInfo = await this.prisma.certifiedPensionInfo.update({
            where: { id },
            data: {
                pendingChanges: uniqueChanges,
            },
        })

        return updatedInfo
    }

    async clearPendingChanges(id: string, paths: string | string[]) {
        const pathsArray = Array.isArray(paths) ? paths : [paths]

        const toUpdate = await this.prisma.certifiedPensionInfo.findUnique({
            where: { id },
        })

        if (!toUpdate) {
            throw new NotFoundException(`CertifiedPensionInfo with ID ${id} not found`)
        }

        const currentChanges = toUpdate.pendingChanges || []
        const updatedChanges = currentChanges.filter(
            (path) => !pathsArray.includes(path)
        )

        return this.prisma.certifiedPensionInfo.update({
            where: { id },
            data: { pendingChanges: updatedChanges },
        })
    }
}