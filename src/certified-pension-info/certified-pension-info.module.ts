import { Module } from '@nestjs/common'
import { CertifiedPensionInfoService } from './certified-pension-info.service'
import { CertifiedPensionInfoResolver } from './certified-pension-info.resolver'
import { AuthModule } from '../auth/auth.module'

@Module({
    imports: [AuthModule],
    providers: [CertifiedPensionInfoResolver, CertifiedPensionInfoService],
    exports: [CertifiedPensionInfoService],
})
export class CertifiedPensionInfoModule {}
