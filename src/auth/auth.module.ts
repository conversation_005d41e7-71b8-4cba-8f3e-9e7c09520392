import { Module } from '@nestjs/common'
import { AuthService } from './auth.service'
import { AuthResolver } from './auth.resolver'
import { ConfigModule } from '@nestjs/config'
import { EmailHandlerModule } from '../email-handler/email-handler.module'

@Module({
    imports: [ConfigModule, EmailHandlerModule],
    exports: [AuthService],
    providers: [AuthService, AuthResolver],
})
export class AuthModule {}
