import { InputType, Field } from '@nestjs/graphql'
import { IsOptional, IsString } from 'class-validator'

@InputType()
export class RegisterDto {
    @Field({ nullable: true })
    @IsString({ message: 'firebaseUid must be a string.' })
    firebaseUid?: string
}

@InputType()
export class LoginRequestDto {
    @Field({ nullable: true })
    @IsString({ message: 'token must be a string.' })
    token?: string
}

@InputType()
export class FirebaseCreateUserDto {
    @Field({ nullable: false })
    @IsString()
    email?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    emailVerified?: boolean

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    phoneNumber?: string

    @Field({ nullable: false })
    @IsString()
    password?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    displayName?: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    avatarImageUrl?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    disabled?: boolean
}
