import { ObjectType, Field } from '@nestjs/graphql'
import { User } from '../../user/entities/user.entity'
import { IsString } from 'class-validator'

@ObjectType()
export class FirebaseUserDto {
    @Field({ nullable: true })
    @IsString()
    name?: string

    @Field({ nullable: true })
    role?: string

    @Field({ nullable: true })
    roleId?: string

    @Field({ nullable: true })
    pensionUserId?: string

    @Field({ nullable: true })
    iss?: string

    @Field({ nullable: true })
    aud?: string

    @Field({ nullable: true })
    auth_time?: number

    @Field({ nullable: true })
    user_id?: string

    @Field({ nullable: true })
    sub?: string

    @Field({ nullable: true })
    iat?: number

    @Field({ nullable: true })
    exp?: number

    @Field({ nullable: true })
    email?: string

    @Field({ nullable: true })
    email_verified?: boolean

    @Field({ nullable: true })
    uid?: string
}
@ObjectType()
export class ErrorType {
    @Field()
    message: string

    @Field({ nullable: true })
    code?: string
}

@ObjectType()
export class RegisterResponse {
    @Field(() => User, { nullable: true }) // Assuming User is another ObjectType you have
    user?: User

    @Field(() => ErrorType, { nullable: true })
    error?: ErrorType
}

@ObjectType()
export class LoginResponse {
    @Field(() => FirebaseUserDto, { nullable: true })
    user: FirebaseUserDto

    @Field(() => ErrorType, { nullable: true })
    error?: ErrorType
}
@ObjectType()
export class ClaimsResponse {
    @Field(() => FirebaseUserDto, { nullable: true })
    claims?: FirebaseUserDto

    @Field(() => ErrorType, { nullable: true })
    error?: ErrorType
}

@ObjectType()
export class FirebaseUserResponse {
    @Field()
    uid: string
}
