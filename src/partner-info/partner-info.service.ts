import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { BaseService } from '../common/base.service'
import { CreatePartnerInfoInput } from './dto/create-partner-info.input'
import { UpdatePartnerInfoInput } from './dto/update-partner-info.input'

@Injectable()
export class PartnerInfoService extends BaseService {
    constructor(private readonly prisma: PrismaService) {
        super()
    }

    create(createPartnerInfoInput: CreatePartnerInfoInput) {
        return this.prisma.partnerInfo.create({
            data: createPartnerInfoInput,
            include: {
                personalInfo: true,
            },
        })
    }

    findAll() {
        return this.prisma.partnerInfo.findMany({
            include: {
                personalInfo: true,
            },
        })
    }

    findOne(id: string) {
        const partnerInfo = this.prisma.partnerInfo.findUnique({
            where: { id },
            include: {
                personalInfo: true,
            },
        })
        if (!partnerInfo) {
            throw new NotFoundException(`PartnerInfo with ID ${id} not found`)
        }
        return partnerInfo
    }

    update(updatePartnerInfoInput: UpdatePartnerInfoInput) {
        const partnerInfo = this.prisma.partnerInfo.findUnique({
            where: { id: updatePartnerInfoInput.id },
        })
        if (!partnerInfo) {
            throw new NotFoundException(
                `PartnerInfo with ID ${updatePartnerInfoInput.id} not found`
            )
        }

        return this.prisma.partnerInfo.update({
            where: { id: updatePartnerInfoInput.id },
            data: updatePartnerInfoInput,
            include: {
                personalInfo: true,
            },
        })
    }

    async changeUpdate(changes: any) {
        const { entityId, newValue, path } = changes

        const value = this.handleDateFields(path, newValue)

        const updateData = {
            [path]: value,
        }

        const updatedPartner = await this.prisma.partnerInfo.update({
            where: { id: entityId },
            data: updateData,
            include: {
                personalInfo: true,
            },
        })

        return updatedPartner
    }

    async clearPendingChanges(id: string, paths: string | string[]) {
        const pathsArray = Array.isArray(paths) ? paths : [paths]

        const toUpdate = await this.prisma.partnerInfo.findUnique({
            where: { id },
        })

        if (!toUpdate) {
            throw new NotFoundException(`PartnerInfo with ID ${id} not found`)
        }

        const currentChanges = toUpdate.pendingChanges || []

        const updatedChanges = currentChanges.filter(
            (path) => !pathsArray.includes(path)
        )

        return this.prisma.partnerInfo.update({
            where: { id },
            data: { pendingChanges: updatedChanges },
        })
    }

    async updatePendingChanges(id: string, changes: string[]) {
        const partnerInfo = await this.prisma.partnerInfo.findUnique({
            where: { id },
        })

        if (!partnerInfo) {
            throw new NotFoundException(`PartnerInfo with ID ${id} not found`)
        }

        const currentChanges = partnerInfo.pendingChanges || []
        const uniqueChanges = [...new Set([...currentChanges, ...changes])]

        const updatedPartnerInfo = await this.prisma.partnerInfo.update({
            where: { id },
            data: {
                pendingChanges: uniqueChanges,
            },
        })

        return updatedPartnerInfo
    }

    async updateFieldByParticipantId(data: {
        participantId: string
        path: string
        newValue: any
    }) {
        const { participantId, path, newValue } = data

        // For partner info, we need to find through personalInfo
        const personalInfo = await this.prisma.personalInfo.findFirst({
            where: { participantId },
            include: { partnerInfo: true },
        })

        if (!personalInfo || !personalInfo.partnerInfo?.length) {
            throw new NotFoundException(
                `No partner info found for participant ID ${participantId}`
            )
        }

        // For simplicity, update the current partner (isCurrent=true) or the first one if none is marked as current
        let partnerToUpdate = personalInfo.partnerInfo.find(
            (partner) => partner.isCurrent === true
        )
        if (!partnerToUpdate && personalInfo.partnerInfo.length > 0) {
            partnerToUpdate = personalInfo.partnerInfo[0]
        }

        if (!partnerToUpdate) {
            throw new NotFoundException(
                `No suitable partner found for participant ID ${participantId}`
            )
        }

        // Update the specific field
        const updateData = {
            [path]: newValue,
        }

        return this.prisma.partnerInfo.update({
            where: { id: partnerToUpdate.id },
            data: updateData,
        })
    }

    remove(id: string) {
        const partnerInfo = this.prisma.partnerInfo.findUnique({
            where: { id },
        })
        if (!partnerInfo) {
            throw new NotFoundException(`PartnerInfo with ID ${id} not found`)
        }
        return this.prisma.partnerInfo.delete({
            where: { id },
            include: {
                personalInfo: true,
            },
        })
    }
}
