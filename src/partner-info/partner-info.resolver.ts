import { Resolver, Query, Mutation, Args, ID } from '@nestjs/graphql';
import { PartnerInfoService } from './partner-info.service';
import { PartnerInfo } from './entities/partner-info.entity';
import { CreatePartnerInfoInput } from './dto/create-partner-info.input';
import { UpdatePartnerInfoInput } from './dto/update-partner-info.input';

@Resolver(() => PartnerInfo)
export class PartnerInfoResolver {
  constructor(private readonly partnerInfoService: PartnerInfoService) { }

  @Mutation(() => PartnerInfo)
  createPartnerInfo(@Args('createPartnerInfoInput') createPartnerInfoInput: CreatePartnerInfoInput) {
    return this.partnerInfoService.create(createPartnerInfoInput);
  }

  @Query(() => [PartnerInfo], { name: 'partnerInfos' })
  findAll() {
    return this.partnerInfoService.findAll();
  }

  @Query(() => PartnerInfo, { name: 'partnerInfo' })
  findOne(@Args('id', { type: () => ID }) id: string) {
    return this.partnerInfoService.findOne(id);
  }

  @Mutation(() => PartnerInfo)
  updatePartnerInfo(@Args('updatePartnerInfoInput') updatePartnerInfoInput: UpdatePartnerInfoInput) {
    return this.partnerInfoService.update(updatePartnerInfoInput);
  }

  @Mutation(() => PartnerInfo)
  removePartnerInfo(@Args('id', { type: () => ID }) id: string) {
    return this.partnerInfoService.remove(id);
  }
}
