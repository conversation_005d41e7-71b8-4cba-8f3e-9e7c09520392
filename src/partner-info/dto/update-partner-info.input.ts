import { InputType, Field, ID } from '@nestjs/graphql'
import { IsString, IsBoolean, IsOptional, IsDate, IsUUID } from 'class-validator'

@InputType()
export class UpdatePartnerInfoInput {
  @Field(() => ID)
  @IsUUID()
  id: string

  @Field()
  @IsUUID()
  personalInfoId: string

  @Field()
  @IsBoolean()
  isCurrent: boolean

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  firstName?: string

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  lastName?: string

  @Field(() => Date, { nullable: true })
  @IsOptional()
  @IsDate()
  dateOfBirth?: Date

  @Field()
  @IsBoolean()
  isDeceased: boolean

  @Field(() => Date, { nullable: true })
  @IsOptional()
  @IsDate()
  startDate?: Date
}
