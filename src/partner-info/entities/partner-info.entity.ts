import { ObjectType, Field, ID } from '@nestjs/graphql';

@ObjectType()
export class PartnerInfo {
  @Field(() => ID)
  id: string;

  @Field()
  personalInfoId: string;

  @Field()
  isCurrent: boolean;

  @Field({ nullable: true })
  firstName?: string;

  @Field({ nullable: true })
  lastName?: string;

  @Field(() => Date, { nullable: true })
  dateOfBirth?: Date;

  @Field()
  isDeceased: boolean;

  @Field(() => Date, { nullable: true })
  startDate?: Date;

  @Field(() => [String], { nullable: true })
  pendingChanges?: string[];
}
