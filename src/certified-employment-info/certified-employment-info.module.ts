import { Module } from '@nestjs/common'
import { CertifiedEmploymentInfoService } from './certified-employment-info.service'
import { CertifiedEmploymentInfoResolver } from './certified-employment-info.resolver'
import { AuthModule } from '../auth/auth.module'

@Module({
    imports: [AuthModule],
    providers: [
        CertifiedEmploymentInfoResolver,
        CertifiedEmploymentInfoService,
    ],
    exports: [CertifiedEmploymentInfoService],
})
export class CertifiedEmploymentInfoModule {}
