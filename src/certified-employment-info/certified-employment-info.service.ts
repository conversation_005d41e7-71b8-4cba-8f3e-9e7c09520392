import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CertifiedEmploymentInfo } from '../certified-data/entities/certified-employment-info.entity'
import { CreateCertifiedEmploymentInfoInput } from '../certified-data/dto/create-certified-employment-info.input'
import { BaseService } from '../common/base.service'

@Injectable()
export class CertifiedEmploymentInfoService extends BaseService {
    constructor(private readonly prisma: PrismaService) {
        super()
    }

    async findOne(id: string) {
        const certifiedEmploymentInfo =
            await this.prisma.certifiedEmploymentInfo.findUnique({
                where: { id },
                include: {
                    certifiedData: true,
                },
            })

        if (!certifiedEmploymentInfo) {
            throw new NotFoundException(
                `CertifiedEmploymentInfo with ID ${id} not found`
            )
        }

        return certifiedEmploymentInfo
    }

    async findByCertifiedDataId(certifiedDataId: string) {
        const certifiedEmploymentInfo =
            await this.prisma.certifiedEmploymentInfo.findUnique({
                where: { certifiedDataId },
                include: {
                    certifiedData: true,
                },
            })

        if (!certifiedEmploymentInfo) {
            throw new NotFoundException(
                `CertifiedEmploymentInfo for certifiedDataId ${certifiedDataId} not found`
            )
        }

        return certifiedEmploymentInfo
    }

    async create(
        createCertifiedEmploymentInfoInput: CreateCertifiedEmploymentInfoInput,
        certifiedDataId: string
    ) {
        // Extract certificationRejectReason to handle separately
        const { certificationRejectReason, ...employmentData } = createCertifiedEmploymentInfoInput

        return this.prisma.certifiedEmploymentInfo.create({
            data: {
                ...employmentData,
                certifiedData: {
                    connect: { id: certifiedDataId },
                },
            },
            include: {
                certifiedData: true,
            },
        })
    }

    async changeUpdate(changes: any) {
        const { entityId, newValue, path } = changes

        const value = this.handleDateFields(path, newValue)

        const updateData = {
            [path]: value,
        }

        const updatedInfo = await this.prisma.certifiedEmploymentInfo.update({
            where: { id: entityId },
            data: updateData,
            include: {
                certifiedData: true,
            },
        })

        if (!updatedInfo) {
            throw new NotFoundException(`CertifiedEmploymentInfo with ID ${entityId} not updated`)
        }

        return updatedInfo
    }

    async updatePendingChanges(id: string, changes: string[]) {
        const certifiedInfo = await this.prisma.certifiedEmploymentInfo.findUnique({
            where: { id },
        })

        if (!certifiedInfo) {
            throw new NotFoundException(`CertifiedEmploymentInfo with ID ${id} not found`)
        }

        const currentChanges = certifiedInfo.pendingChanges || []
        const uniqueChanges = [...new Set([...currentChanges, ...changes])]

        const updatedInfo = await this.prisma.certifiedEmploymentInfo.update({
            where: { id },
            data: {
                pendingChanges: uniqueChanges,
            },
        })

        return updatedInfo
    }

    async clearPendingChanges(id: string, paths: string | string[]) {
        const pathsArray = Array.isArray(paths) ? paths : [paths]

        const toUpdate = await this.prisma.certifiedEmploymentInfo.findUnique({
            where: { id },
        })

        if (!toUpdate) {
            throw new NotFoundException(`CertifiedEmploymentInfo with ID ${id} not found`)
        }

        const currentChanges = toUpdate.pendingChanges || []
        const updatedChanges = currentChanges.filter(
            (path) => !pathsArray.includes(path)
        )

        return this.prisma.certifiedEmploymentInfo.update({
            where: { id },
            data: { pendingChanges: updatedChanges },
        })
    }
}
