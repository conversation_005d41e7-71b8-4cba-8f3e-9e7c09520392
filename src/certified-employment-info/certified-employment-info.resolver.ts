import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql'
import { UseGuards } from '@nestjs/common'
import { CertifiedEmploymentInfoService } from './certified-employment-info.service'
import { CertifiedEmploymentInfo } from '../certified-data/entities'
import { CreateCertifiedEmploymentInfoInput } from '../certified-data/dto'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'

@Resolver(() => CertifiedEmploymentInfo)
export class CertifiedEmploymentInfoResolver {
    constructor(
        private readonly certifiedEmploymentInfoService: CertifiedEmploymentInfoService
    ) {}

    @UseGuards(GraphqlAuthGuard)
    @Query(() => CertifiedEmploymentInfo)
    async certifiedEmploymentInfo(@Args('id') id: string) {
        return this.certifiedEmploymentInfoService.findOne(id)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => CertifiedEmploymentInfo)
    async certifiedEmploymentInfoByCertifiedDataId(
        @Args('certifiedDataId') certifiedDataId: string
    ) {
        return this.certifiedEmploymentInfoService.findByCertifiedDataId(
            certifiedDataId
        )
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => CertifiedEmploymentInfo)
    async createCertifiedEmploymentInfo(
        @Args('createCertifiedEmploymentInfoInput')
        createCertifiedEmploymentInfoInput: CreateCertifiedEmploymentInfoInput,
        @Args('certifiedDataId') certifiedDataId: string,
        @Context() context: any
    ) {
        return this.certifiedEmploymentInfoService.create(
            createCertifiedEmploymentInfoInput,
            certifiedDataId
        )
    }
}
