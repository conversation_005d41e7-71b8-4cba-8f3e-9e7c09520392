import { Test, TestingModule } from '@nestjs/testing';
import { SalaryEntryResolver } from './salary-entry.resolver';
import { SalaryEntryService } from './salary-entry.service';

describe('SalaryEntryResolver', () => {
  let resolver: SalaryEntryResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [SalaryEntryResolver, SalaryEntryService],
    }).compile();

    resolver = module.get<SalaryEntryResolver>(SalaryEntryResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
