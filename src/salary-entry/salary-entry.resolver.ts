import { Resolver, Query, Mutation, Args } from '@nestjs/graphql'
import { SalaryEntryService } from './salary-entry.service'
import { SalaryEntry } from './entities/salary-entry.entity'
import { CreateSalaryEntryInput } from './dto/create-salary-entry.input'
import { UpdateSalaryEntryInput } from './dto/update-salary-entry.input'

@Resolver(() => SalaryEntry)
export class SalaryEntryResolver {
    constructor(private readonly salaryEntryService: SalaryEntryService) {}

    @Mutation(() => SalaryEntry)
    async createSalaryEntry(
        @Args('createSalaryEntryInput')
        createSalaryEntryInput: CreateSalaryEntryInput
    ) {
        return this.salaryEntryService.create(createSalaryEntryInput)
    }

    @Query(() => [SalaryEntry], { name: 'salaryEntries' })
    async findAll() {
        return this.salaryEntryService.findAll()
    }

    @Query(() => SalaryEntry, { name: 'salaryEntry' })
    async findOne(@Args('id') id: string) {
        return this.salaryEntryService.findOne(id)
    }

    @Mutation(() => SalaryEntry)
    async updateSalaryEntry(
        @Args('updateSalaryEntryInput')
        updateSalaryEntryInput: UpdateSalaryEntryInput
    ) {
        return this.salaryEntryService.update(updateSalaryEntryInput)
    }

    @Mutation(() => SalaryEntry)
    async deleteSalaryEntry(@Args('id') id: string) {
        return this.salaryEntryService.delete(id)
    }
}
