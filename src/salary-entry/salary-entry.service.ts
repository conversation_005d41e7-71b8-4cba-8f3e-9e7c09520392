import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateSalaryEntryInput } from './dto/create-salary-entry.input'
import { UpdateSalaryEntryInput } from './dto/update-salary-entry.input'
import { SalaryEntry } from './entities/salary-entry.entity'

@Injectable()
export class SalaryEntryService {
    constructor(private readonly prisma: PrismaService) {}

    async create(createSalaryEntryInput: CreateSalaryEntryInput) {
        const salaryEntry = await this.prisma.salaryEntry.create({
            data: {
                ...createSalaryEntryInput,
            },
            include: {
                employmentInfo: true,
            },
        })

        if (!salaryEntry) {
            throw new NotFoundException('SalaryEntry not created')
        }

        return salaryEntry
    }

    async findAll() {
        return this.prisma.salaryEntry.findMany({
            include: {
                employmentInfo: true,
            },
        })
    }

    async findOne(id: string) {
        const salaryEntry = await this.prisma.salaryEntry.findUnique({
            where: { id },
            include: {
                employmentInfo: true,
            },
        })

        if (!salaryEntry) {
            throw new NotFoundException('SalaryEntry not found')
        }

        return salaryEntry
    }

    async update(updateSalaryEntryInput: UpdateSalaryEntryInput) {
        const salaryEntry = await this.prisma.salaryEntry.update({
            where: { id: updateSalaryEntryInput.id },
            data: {
                ...updateSalaryEntryInput,
            },
            include: {
                employmentInfo: true,
            },
        })

        if (!salaryEntry) {
            throw new NotFoundException('SalaryEntry not updated')
        }

        return salaryEntry
    }

    async updatePendingChanges(id: string, changes: string[]) {
        const salaryEntry = await this.prisma.salaryEntry.findUnique({
            where: { id },
        })

        if (!salaryEntry) {
            throw new NotFoundException(`Child with ID ${id} not found`)
        }

        const currentChanges = salaryEntry.pendingChanges || []
        const uniqueChanges = [...new Set([...currentChanges, ...changes])]

        const salaryEntryInfo = await this.prisma.salaryEntry.update({
            where: { id },
            data: {
                pendingChanges: uniqueChanges,
            },
        })

        return salaryEntryInfo
    }

    async changeUpdate(changes: any) {
        const { entityId, newValue, path, oldValue, partTimePercentage } =
            changes
        const newValFormatted = parseFloat(newValue)
        const updateData = {
            [path]: newValFormatted,
        }

        if (oldValue === 'new') {
            if (!entityId) {
                throw new NotFoundException(
                    'Missing required fields to create a new SalaryEntry'
                )
            }
            const year = new Date().getFullYear() + 1

            const existingSalaryEntry =
                await this.prisma.salaryEntry.findUnique({
                    where: {
                        employmentInfoId_year: {
                            employmentInfoId: entityId,
                            year,
                        },
                    },
                })

            if (existingSalaryEntry) {
                const updatedPartner = await this.prisma.salaryEntry.update({
                    where: { id: existingSalaryEntry.id },
                    data: updateData,
                })
                return updatedPartner
            }

            const createData: CreateSalaryEntryInput = {
                employmentInfoId: entityId,
                year: year,
                amount: path === 'amount' ? newValFormatted : 0,
                partTimePercentage:
                    path === 'partTimePercentage'
                        ? newValFormatted
                        : partTimePercentage
                          ? parseFloat(partTimePercentage)
                          : 100,
            }

            return this.create(createData)
        }

        const updatedPartner = await this.prisma.salaryEntry.update({
            where: { id: entityId },
            data: updateData,
        })

        if (!updatedPartner) {
            throw new NotFoundException('SalaryEntry not updated')
        }

        return updatedPartner
    }

    async clearPendingChanges(id: string, paths: string | string[]) {
        const pathsArray = Array.isArray(paths) ? paths : [paths]

        const toUpdate = await this.prisma.salaryEntry.findUnique({
            where: { id },
        })

        if (!toUpdate) {
            console.log(`Salary Entry with ID ${id} not found`)
            return true
        }

        const currentChanges = toUpdate.pendingChanges || []

        const updatedChanges = currentChanges.filter(
            (path) => !pathsArray.includes(path)
        )

        return this.prisma.salaryEntry.update({
            where: { id },
            data: { pendingChanges: updatedChanges },
        })
    }

    async delete(id: string) {
        const salaryEntry = await this.prisma.salaryEntry.delete({
            where: { id },
        })

        if (!salaryEntry) {
            throw new NotFoundException('SalaryEntry not deleted')
        }

        return salaryEntry
    }

    async updateFieldByParticipantId(data: {
        participantId: string
        path: string
        newValue: any
    }) {
        const { participantId, path, newValue } = data

        // For salary entries we need to find the record through employment info
        const employmentInfo = await this.prisma.employmentInfo.findFirst({
            where: { participantId },
            include: { salaryEntries: true },
        })

        if (!employmentInfo || !employmentInfo.salaryEntries?.length) {
            throw new NotFoundException(
                `No salary entries found for participant ID ${participantId}`
            )
        }

        // For simplicity, update the most recent salary entry
        // You might need to modify this based on specific business logic
        const mostRecentEntry = employmentInfo.salaryEntries.reduce(
            (latest, current) =>
                latest.year > current.year ? latest : current,
            employmentInfo.salaryEntries[0]
        )

        // Update the specific field
        const updateData = {
            [path]: newValue,
        }

        return this.prisma.salaryEntry.update({
            where: { id: mostRecentEntry.id },
            data: updateData,
        })
    }
}
