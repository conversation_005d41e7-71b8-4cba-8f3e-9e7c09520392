import { ObjectType, Field, ID } from '@nestjs/graphql'
import { EmploymentInfo } from '../../employment-info/entities/employment-info.entity'

@ObjectType()
export class SalaryEntry {
    @Field(() => ID)
    id: string

    @Field(() => EmploymentInfo)
    employmentInfo: EmploymentInfo

    @Field()
    year: number

    @Field()
    amount: number

    @Field()
    partTimePercentage: number

    @Field(() => [String], { nullable: true })
    pendingChanges?: string[]
}
