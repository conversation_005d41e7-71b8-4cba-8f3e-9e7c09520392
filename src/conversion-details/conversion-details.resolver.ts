import { Resolver, Query, Mutation, Args } from '@nestjs/graphql'
import { ConversionDetailsService } from './conversion-details.service'
import { ConversionDetail } from './entities/conversion-detail.entity'
import { CreateConversionDetailsInput } from './dto/create-conversion-detail.input'
import { UpdateConversionDetailsInput } from './dto/update-conversion-detail.input'

@Resolver(() => ConversionDetail)
export class ConversionDetailsResolver {
    constructor(
        private readonly conversionDetailsService: ConversionDetailsService
    ) {}

    @Mutation(() => ConversionDetail)
    async createConversionDetails(
        @Args('createConversionDetailsInput')
        createConversionDetailsInput: CreateConversionDetailsInput
    ) {
        return this.conversionDetailsService.create(
            createConversionDetailsInput
        )
    }

    @Query(() => [ConversionDetail], { name: 'conversionDetails' })
    async findAll() {
        return this.conversionDetailsService.findAll()
    }

    @Query(() => ConversionDetail, { name: 'conversionDetails' })
    async findOne(@Args('id') id: string) {
        return this.conversionDetailsService.findOne(id)
    }

    @Mutation(() => ConversionDetail)
    async updateConversionDetails(
        @Args('updateConversionDetailsInput')
        updateConversionDetailsInput: UpdateConversionDetailsInput
    ) {
        return this.conversionDetailsService.update(
            updateConversionDetailsInput
        )
    }

    @Mutation(() => ConversionDetail)
    async deleteConversionDetails(@Args('id') id: string) {
        return this.conversionDetailsService.delete(id)
    }
}
