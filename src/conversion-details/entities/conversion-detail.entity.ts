import { ObjectType, Field, ID } from '@nestjs/graphql'
import { PensionData } from '../../pension-data/entities/pension-data.entity'

@ObjectType()
export class ConversionDetail {
    @Field(() => ID)
    id: string

    @Field(() => PensionData)
    pensionData: PensionData

    @Field({ nullable: true })
    conversionDate?: Date

    @Field({ nullable: true })
    conversionRate?: number

    @Field({ nullable: true })
    oldAgePensionIncrease?: number

    @Field({ nullable: true })
    partnerPensionIncrease?: number
}
