import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateConversionDetailsInput } from './dto/create-conversion-detail.input'
import { UpdateConversionDetailsInput } from './dto/update-conversion-detail.input'
import { ConversionDetail } from './entities/conversion-detail.entity'

@Injectable()
export class ConversionDetailsService {
    constructor(private readonly prisma: PrismaService) {}

    async create(createConversionDetailsInput: CreateConversionDetailsInput) {
        const conversionDetails = await this.prisma.conversionDetails.create({
            data: {
                ...createConversionDetailsInput,
            },
            include: {
                pensionData: true,
            },
        })

        if (!conversionDetails) {
            throw new NotFoundException('ConversionDetails not created')
        }

        return conversionDetails
    }

    async findAll() {
        return this.prisma.conversionDetails.findMany({
            include: {
                pensionData: true,
            },
        })
    }

    async findOne(id: string) {
        const conversionDetails =
            await this.prisma.conversionDetails.findUnique({
                where: { id },
                include: {
                    pensionData: true,
                },
            })

        if (!conversionDetails) {
            throw new NotFoundException('ConversionDetails not found')
        }

        return conversionDetails
    }

    async update(updateConversionDetailsInput: UpdateConversionDetailsInput) {
        const conversionDetails = await this.prisma.conversionDetails.update({
            where: { id: updateConversionDetailsInput.id },
            data: {
                ...updateConversionDetailsInput,
            },
            include: {
                pensionData: true,
            },
        })

        if (!conversionDetails) {
            throw new NotFoundException('ConversionDetails not updated')
        }

        return conversionDetails
    }

    async delete(id: string) {
        const conversionDetails = await this.prisma.conversionDetails.delete({
            where: { id },
        })

        if (!conversionDetails) {
            throw new NotFoundException('ConversionDetails not deleted')
        }

        return conversionDetails
    }
}
