import { Injectable, NotFoundException } from '@nestjs/common'
import { CreateCertifiedPartnerInfoInput } from './dto/create-certified-partner-info.input'
import { UpdateCertifiedPartnerInfoInput } from './dto/update-certified-partner-info.input'
import { PrismaService } from '../prisma.service'
import { BaseService } from '../common/base.service'

@Injectable()
export class CertifiedPartnerInfoService extends BaseService {
  constructor(private readonly prisma: PrismaService) {
    super()
  }

  async findOne(id: string) {
    const certifiedPartnerInfo =
      await this.prisma.certifiedPartnerInfo.findUnique({
        where: { id },
        include: {
          certifiedData: true,
          certificationRejectReason: true,
        },
      })

    if (!certifiedPartnerInfo) {
      throw new NotFoundException(
        `CertifiedPartnerInfo with ID ${id} not found`
      )
    }

    return certifiedPartnerInfo
  }

  async changeUpdate(changes: any) {
    const { entityId, newValue, path } = changes

    const value = this.handleDateFields(path, newValue)

    const updateData = {
      [path]: value,
    }

    const updatedInfo = await this.prisma.certifiedPartnerInfo.update({
      where: { id: entityId },
      data: updateData,
      include: {
        certifiedData: true,
      },
    })

    if (!updatedInfo) {
      throw new NotFoundException(
        `CertifiedPartnerInfo with ID ${entityId} not updated`
      )
    }

    return updatedInfo
  }

  async updatePendingChanges(id: string, changes: string[]) {
    const certifiedInfo = await this.prisma.certifiedPartnerInfo.findUnique(
      {
        where: { id },
      }
    )

    if (!certifiedInfo) {
      throw new NotFoundException(
        `CertifiedPartnerInfo with ID ${id} not found`
      )
    }

    const currentChanges = certifiedInfo.pendingChanges || []
    const uniqueChanges = [...new Set([...currentChanges, ...changes])]

    const updatedInfo = await this.prisma.certifiedPartnerInfo.update({
      where: { id },
      data: {
        pendingChanges: uniqueChanges,
      },
    })

    return updatedInfo
  }

  async clearPendingChanges(id: string, paths: string | string[]) {
    const pathsArray = Array.isArray(paths) ? paths : [paths]

    const toUpdate = await this.prisma.certifiedPartnerInfo.findUnique({
      where: { id },
    })

    if (!toUpdate) {
      throw new NotFoundException(
        `CertifiedPartnerInfo with ID ${id} not found`
      )
    }

    const currentChanges = toUpdate.pendingChanges || []
    const updatedChanges = currentChanges.filter(
      (path) => !pathsArray.includes(path)
    )

    return this.prisma.certifiedPartnerInfo.update({
      where: { id },
      data: { pendingChanges: updatedChanges },
    })
  }
}
