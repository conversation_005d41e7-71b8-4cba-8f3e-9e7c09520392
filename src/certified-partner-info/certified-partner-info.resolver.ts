import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql'
import { CertifiedPartnerInfoService } from './certified-partner-info.service'
import { CreateCertifiedPartnerInfoInput } from './dto/create-certified-partner-info.input'
import { UpdateCertifiedPartnerInfoInput } from './dto/update-certified-partner-info.input'
import { CertifiedPartnerInfo } from '../certified-data/entities/certified-partner-info.entity'

@Resolver(() => CertifiedPartnerInfo)
export class CertifiedPartnerInfoResolver {
    constructor(
        private readonly certifiedPartnerInfoService: CertifiedPartnerInfoService
    ) {}

    @Query(() => CertifiedPartnerInfo, { name: 'certifiedPartnerInfo' })
    findOne(@Args('id', { type: () => String }) id: string) {
        return this.certifiedPartnerInfoService.findOne(id)
    }
}
