import { Module } from '@nestjs/common'
import { CertifiedPersonalInfoService } from './certified-personal-info.service'
import { CertifiedPersonalInfoResolver } from './certified-personal-info.resolver'
import { AuthModule } from '../auth/auth.module'

@Module({
    imports: [AuthModule],
    providers: [CertifiedPersonalInfoResolver, CertifiedPersonalInfoService],
    exports: [CertifiedPersonalInfoService],
})
export class CertifiedPersonalInfoModule {}
