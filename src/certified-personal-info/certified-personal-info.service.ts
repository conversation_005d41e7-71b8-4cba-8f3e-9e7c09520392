import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CertifiedPersonalInfo } from '../certified-data/entities/certified-personal-info.entity'
import { CreateCertifiedPersonalInfoInput } from '../certified-data/dto/create-certified-personal-info.input'
import { BaseService } from '../common/base.service'

@Injectable()
export class CertifiedPersonalInfoService extends BaseService {
    constructor(private readonly prisma: PrismaService) {
        super()
    }

    async findOne(id: string) {
        const certifiedPersonalInfo =
            await this.prisma.certifiedPersonalInfo.findUnique({
                where: { id },
                include: {
                    certifiedData: true,
                },
            })

        if (!certifiedPersonalInfo) {
            throw new NotFoundException(
                `CertifiedPersonalInfo with ID ${id} not found`
            )
        }

        return certifiedPersonalInfo
    }

    async findByCertifiedDataId(certifiedDataId: string) {
        const certifiedPersonalInfo =
            await this.prisma.certifiedPersonalInfo.findUnique({
                where: { certifiedDataId },
                include: {
                    certifiedData: true,
                },
            })

        if (!certifiedPersonalInfo) {
            throw new NotFoundException(
                `CertifiedPersonalInfo for certifiedDataId ${certifiedDataId} not found`
            )
        }

        return certifiedPersonalInfo
    }

    async create(
        createCertifiedPersonalInfoInput: CreateCertifiedPersonalInfoInput,
        certifiedDataId: string
    ) {
        // Extract certificationRejectReason to handle separately
        const { ...personalData } = createCertifiedPersonalInfoInput

        return this.prisma.certifiedPersonalInfo.create({
            data: {
                ...personalData,
                certificationRejectReason: {
                    create: createCertifiedPersonalInfoInput.certificationRejectReason,
                },
                certifiedData: {
                    connect: { id: certifiedDataId },
                },
            },
            include: {
                certifiedData: true,
            },
        })
    }

    async changeUpdate(changes: any) {
        const { entityId, newValue, path } = changes

        const value = this.handleDateFields(path, newValue)

        const updateData = {
            [path]: value,
        }

        const updatedInfo = await this.prisma.certifiedPersonalInfo.update({
            where: { id: entityId },
            data: updateData,
            include: {
                certifiedData: true,
            },
        })

        if (!updatedInfo) {
            throw new NotFoundException(
                `CertifiedPersonalInfo with ID ${entityId} not updated`
            )
        }

        return updatedInfo
    }

    async updatePendingChanges(id: string, changes: string[]) {
        const certifiedInfo =
            await this.prisma.certifiedPersonalInfo.findUnique({
                where: { id },
            })

        if (!certifiedInfo) {
            throw new NotFoundException(
                `CertifiedPersonalInfo with ID ${id} not found`
            )
        }

        const currentChanges = certifiedInfo.pendingChanges || []
        const uniqueChanges = [...new Set([...currentChanges, ...changes])]

        const updatedInfo = await this.prisma.certifiedPersonalInfo.update({
            where: { id },
            data: {
                pendingChanges: uniqueChanges,
            },
        })

        return updatedInfo
    }

    async clearPendingChanges(id: string, paths: string | string[]) {
        const pathsArray = Array.isArray(paths) ? paths : [paths]

        const toUpdate = await this.prisma.certifiedPersonalInfo.findUnique({
            where: { id },
        })

        if (!toUpdate) {
            throw new NotFoundException(
                `CertifiedPersonalInfo with ID ${id} not found`
            )
        }

        const currentChanges = toUpdate.pendingChanges || []
        const updatedChanges = currentChanges.filter(
            (path) => !pathsArray.includes(path)
        )

        return this.prisma.certifiedPersonalInfo.update({
            where: { id },
            data: { pendingChanges: updatedChanges },
        })
    }
}
