import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql'
import { UseGuards } from '@nestjs/common'
import { CertifiedPersonalInfoService } from './certified-personal-info.service'
import { CertifiedPersonalInfo } from '../certified-data/entities'
import { CreateCertifiedPersonalInfoInput } from '../certified-data/dto'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'

@Resolver(() => CertifiedPersonalInfo)
export class CertifiedPersonalInfoResolver {
    constructor(
        private readonly certifiedPersonalInfoService: CertifiedPersonalInfoService
    ) {}

    @UseGuards(GraphqlAuthGuard)
    @Query(() => CertifiedPersonalInfo)
    async certifiedPersonalInfo(@Args('id') id: string) {
        return this.certifiedPersonalInfoService.findOne(id)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => CertifiedPersonalInfo)
    async certifiedPersonalInfoByCertifiedDataId(
        @Args('certifiedDataId') certifiedDataId: string
    ) {
        return this.certifiedPersonalInfoService.findByCertifiedDataId(
            certifiedDataId
        )
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => CertifiedPersonalInfo)
    async createCertifiedPersonalInfo(
        @Args('createCertifiedPersonalInfoInput')
        createCertifiedPersonalInfoInput: CreateCertifiedPersonalInfoInput,
        @Args('certifiedDataId') certifiedDataId: string,
        @Context() context: any
    ) {
        return this.certifiedPersonalInfoService.create(
            createCertifiedPersonalInfoInput,
            certifiedDataId
        )
    }
}
