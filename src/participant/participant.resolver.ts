import { Resolver, Query, Mutation, Args, Context, Int } from '@nestjs/graphql'
import { UseGuards } from '@nestjs/common'
import { ParticipantService } from './participant.service'
import { Participant } from './entities/participant.entity'
import { CreateParticipantInput } from './dto/create-participant.input'
import { UpdateParticipantInput } from './dto/update-participant.input'
import { FindAllParticipantsInput } from './dto/find-all-participants.input'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'
import { PaginatedParticipants } from './entities/participant-paginated.entity'

@Resolver(() => Participant)
export class ParticipantResolver {
    constructor(private readonly participantService: ParticipantService) {}

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => Participant)
    async createParticipant(
        @Args('createParticipantInput')
        createParticipantInput: CreateParticipantInput,
        @Context() context: any
    ) {
        const userId = context.req.user.id
        return this.participantService.create(createParticipantInput, userId)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => PaginatedParticipants, {
        name: 'getAllParticipantsWithFilter',
    })
    async findAll(
        @Args('findAllParticipantsInput', { nullable: true })
        findAllParticipantsInput: FindAllParticipantsInput = {}
    ) {
        return this.participantService.findAll(findAllParticipantsInput)
    }

    @Query(() => [Participant], { name: 'getAllParticipants' })
    async findAllSlim() {
        return this.participantService.findAllSlim()
    }

    @Query(() => Participant, { name: 'getParticipantById' })
    async findOne(@Args('id', { type: () => String }) id: string) {
        return this.participantService.findOne(id)
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => Participant)
    async updateParticipant(
        @Args('updateParticipantInput')
        updateParticipantInput: UpdateParticipantInput,
        @Context() context: any
    ) {
        const userId = context.req.user.id
        return this.participantService.update(
            updateParticipantInput.id,
            updateParticipantInput,
            userId
        )
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => Participant)
    async removeParticipant(
        @Args('id', { type: () => String }) id: string,
        @Context() context: any
    ) {
        const userId = context.req.user.id
        return this.participantService.remove(id, userId)
    }

    // We might need additional queries for various participant-related operations
    @UseGuards(GraphqlAuthGuard)
    @Query(() => Int, { name: 'totalParticipants' })
    async countParticipants(
        @Args('status', { type: () => String, nullable: true }) status?: string
    ) {
        const result = await this.participantService.findAll({
            status,
            take: 1, // Just need count, not actual items
        })
        return result.totalCount
    }
}
