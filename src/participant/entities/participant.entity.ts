import { ObjectType, Field, ID } from '@nestjs/graphql'
import { EmploymentInfo } from '../../employment-info/entities/employment-info.entity'
import { PensionInfo } from '../../pension-info/entities/pension-info.entity'
import { ChangeProposal } from '../../change-proposal/entities/change-proposal.entity'
import { CertifiedData } from '../../certified-data/entities/certified-data.entity'
import { PensionData } from '../../pension-data/entities/pension-data.entity'
import { PersonalInfo } from '../../personal-info/entities/personal-info.entity'
import { Document } from '../../documents/entities/document.entity'
import { PartnerInfo } from '../../partner-info/entities/partner-info.entity'

@ObjectType()
export class Participant {
    @Field(() => ID)
    id: string

    @Field()
    createdAt: Date

    @Field()
    createdBy: string

    @Field(() => String)
    status: string

    @Field()
    updatedAt: Date

    @Field()
    updatedBy: string

    @Field({ nullable: true })
    lastModified?: Date

    @Field(() => PersonalInfo, { nullable: true })
    personalInfo?: PersonalInfo

    @Field(() => [Document], { nullable: true })
    documents?: Document[]

    @Field(() => EmploymentInfo, { nullable: true })
    employmentInfo?: EmploymentInfo

    @Field(() => PensionInfo, { nullable: true })
    pensionInfo?: PensionInfo

    @Field(() => PensionData, { nullable: true })
    pensionData?: PensionData

    @Field(() => [ChangeProposal], { nullable: true })
    changeProposals?: ChangeProposal[]

    @Field(() => [CertifiedData], { nullable: true })
    certifiedData?: CertifiedData[]
}
