import { Module } from '@nestjs/common'
import { ParticipantService } from './participant.service'
import { ParticipantResolver } from './participant.resolver'
import { AuthModule } from '../auth/auth.module'
import { AuditLogModule } from '../audit-log/audit-log.module'

@Module({
    imports: [AuthModule, AuditLogModule],
    providers: [ParticipantResolver, ParticipantService],
    exports: [ParticipantService],
})
export class ParticipantModule {}
