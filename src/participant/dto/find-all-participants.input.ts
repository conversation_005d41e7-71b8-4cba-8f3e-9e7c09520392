import { InputType, Field } from '@nestjs/graphql'
import { IsOptional, IsString } from 'class-validator'

@InputType()
export class FindAllParticipantsInput {
    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    status?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    searchTerm?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    sortBy?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    sortOrder?: 'asc' | 'desc'

    @Field({ nullable: true })
    @IsOptional()
    skip?: number

    @Field({ nullable: true })
    @IsOptional()
    take?: number
}
