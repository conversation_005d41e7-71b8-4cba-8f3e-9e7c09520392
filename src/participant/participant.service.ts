import {
    BadRequestException,
    Injectable,
    NotFoundException,
} from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateParticipantInput } from './dto/create-participant.input'
import { UpdateParticipantInput } from './dto/update-participant.input'
import { FindAllParticipantsInput } from './dto/find-all-participants.input'
import { AuditLogService } from '../audit-log/audit-log.service'

@Injectable()
export class ParticipantService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly auditLogService: AuditLogService
    ) {}

    private getParticipantIncludes() {
        return {
            personalInfo: {
                include: {
                    address: true,
                    partnerInfo: true,
                    children: true,
                },
            },
            documents: true,
            employmentInfo: {
                include: {
                    salaryEntries: true,
                },
            },
            pensionInfo: true,
            pensionData: {
                include: {
                    pensionParameters: true,
                    voluntaryContributions: true,
                    annualAccrual: true,
                    conversionDetails: true,
                },
            },
            certifiedData: true,
        }
    }

    async create(
        createParticipantInput: CreateParticipantInput,
        userId: string
    ) {
        const {
            personalInfo,
            employmentInfo,
            pensionInfo,
            pensionData,
            ...participantData
        } = createParticipantInput

        try {
            const participant = await this.prisma.$transaction(async (tx) => {
                const newParticipant = await tx.participant.create({
                    data: {
                        ...participantData,
                    },
                })

                if (personalInfo) {
                    const { address, partnerInfo, children, ...personalData } =
                        personalInfo

                    const personalInfoRecord = await tx.personalInfo.create({
                        data: {
                            ...personalData,
                            participantId: newParticipant.id,
                        },
                    })

                    // Create address if provided
                    if (address) {
                        await tx.address.create({
                            data: {
                                ...address,
                                personalInfoId: personalInfoRecord.id,
                            },
                        })
                    }

                    // Create partner info if provided
                    if (partnerInfo && partnerInfo.length > 0) {
                        await Promise.all(
                            partnerInfo.map((partner) =>
                                tx.partnerInfo.create({
                                    data: {
                                        ...partner,
                                        personalInfoId: personalInfoRecord.id,
                                    },
                                })
                            )
                        )
                    }

                    // Create children if provided
                    if (children && children.length > 0) {
                        await Promise.all(
                            children.map((child) =>
                                tx.child.create({
                                    data: {
                                        ...child,
                                        personalInfoId: personalInfoRecord.id,
                                    },
                                })
                            )
                        )
                    }
                }

                // Create employment info if provided
                if (employmentInfo) {
                    const { salaryEntries, ...employmentData } = employmentInfo

                    const employmentInfoRecord = await tx.employmentInfo.create(
                        {
                            data: {
                                ...employmentData,
                                participantId: newParticipant.id,
                            },
                        }
                    )

                    // Create salary entries if provided
                    if (salaryEntries && salaryEntries.length > 0) {
                        await Promise.all(
                            salaryEntries.map((entry) =>
                                tx.salaryEntry.create({
                                    data: {
                                        ...entry,
                                        employmentInfoId:
                                            employmentInfoRecord.id,
                                    },
                                })
                            )
                        )
                    }
                }

                // Create pension info if provided
                if (pensionInfo) {
                    await tx.pensionInfo.create({
                        data: {
                            ...pensionInfo,
                            participantId: newParticipant.id,
                        },
                    })
                }

                // Create pension data if provided
                if (pensionData) {
                    const {
                        voluntaryContributions,
                        annualAccrual,
                        conversionDetails,
                        pensionParameters,
                        ...pensionDataFields
                    } = pensionData

                    const pensionDataRecord = await tx.pensionData.create({
                        data: {
                            ...pensionDataFields,
                            participantId: newParticipant.id,
                        },
                    })

                    // Create voluntary contributions if provided
                    if (
                        voluntaryContributions &&
                        voluntaryContributions.length > 0
                    ) {
                        await Promise.all(
                            voluntaryContributions.map((contribution) =>
                                tx.voluntaryContribution.create({
                                    data: {
                                        ...contribution,
                                        pensionDataId: pensionDataRecord.id,
                                    },
                                })
                            )
                        )
                    }

                    // Create annual accrual if provided
                    if (annualAccrual) {
                        await tx.annualAccrual.create({
                            data: {
                                ...annualAccrual,
                                pensionDataId: pensionDataRecord.id,
                            },
                        })
                    }

                    // Create conversion details if provided
                    if (conversionDetails) {
                        await tx.conversionDetails.create({
                            data: {
                                ...conversionDetails,
                                pensionDataId: pensionDataRecord.id,
                            },
                        })
                    }
                }

                return newParticipant
            })

            return this.findOne(participant.id)
        } catch (error) {
            throw new BadRequestException(
                `Failed to create participant: ${error.message}`
            )
        }
    }

    //Update Participant below
    async update(
        id: string,
        updateParticipantInput: UpdateParticipantInput,
        userId: string
    ) {
        const existingParticipant = await this.findOne(id)
        if (!existingParticipant) {
            throw new NotFoundException(`Participant with ID ${id} not found`)
        }

        const {
            personalInfo,
            employmentInfo,
            pensionInfo,
            pensionData,
            ...participantData
        } = updateParticipantInput

        try {
            const participant = await this.prisma.$transaction(async (tx) => {
                // Update main participant record
                const updatedParticipant = await tx.participant.update({
                    where: { id },
                    data: {
                        ...participantData,
                        updatedBy: userId,
                        lastModified: new Date(),
                    },
                })

                // Update personal info if provided
                if (personalInfo) {
                    const { address, partnerInfo, children, ...personalData } =
                        personalInfo

                    await tx.personalInfo.upsert({
                        where: { participantId: id },
                        create: {
                            ...personalData,
                            participantId: id,
                        },
                        update: personalData,
                    })

                    const personalInfoRecord = await tx.personalInfo.findUnique(
                        {
                            where: { participantId: id },
                        }
                    )

                    // Update address if provided
                    if (address && personalInfoRecord) {
                        await tx.address.upsert({
                            where: { personalInfoId: personalInfoRecord.id },
                            create: {
                                ...address,
                                personalInfoId: personalInfoRecord.id,
                            },
                            update: address,
                        })
                    }

                    // Handle partner info updates
                    if (partnerInfo && personalInfoRecord) {
                        // Delete existing partners and recreate (simpler approach)
                        await tx.partnerInfo.deleteMany({
                            where: { personalInfoId: personalInfoRecord.id },
                        })

                        if (partnerInfo.length > 0) {
                            await Promise.all(
                                partnerInfo.map((partner) =>
                                    tx.partnerInfo.create({
                                        data: {
                                            ...partner,
                                            personalInfoId:
                                                personalInfoRecord.id,
                                        },
                                    })
                                )
                            )
                        }
                    }

                    // Handle children updates
                    if (children && personalInfoRecord) {
                        // Delete existing children and recreate
                        await tx.child.deleteMany({
                            where: { personalInfoId: personalInfoRecord.id },
                        })

                        if (children.length > 0) {
                            await Promise.all(
                                children.map((child) =>
                                    tx.child.create({
                                        data: {
                                            ...child,
                                            personalInfoId:
                                                personalInfoRecord.id,
                                        },
                                    })
                                )
                            )
                        }
                    }
                }

                // Update employment info if provided
                if (employmentInfo) {
                    const { salaryEntries, ...employmentData } = employmentInfo

                    await tx.employmentInfo.upsert({
                        where: { participantId: id },
                        create: {
                            ...employmentData,
                            participantId: id,
                        },
                        update: employmentData,
                    })

                    const employmentInfoRecord =
                        await tx.employmentInfo.findUnique({
                            where: { participantId: id },
                        })

                    // Handle salary entries updates
                    if (salaryEntries && employmentInfoRecord) {
                        // Delete existing entries and recreate
                        await tx.salaryEntry.deleteMany({
                            where: {
                                employmentInfoId: employmentInfoRecord.id,
                            },
                        })

                        if (salaryEntries.length > 0) {
                            await Promise.all(
                                salaryEntries.map((entry) =>
                                    tx.salaryEntry.create({
                                        data: {
                                            ...entry,
                                            employmentInfoId:
                                                employmentInfoRecord.id,
                                        },
                                    })
                                )
                            )
                        }
                    }
                }

                // Update pension info if provided
                if (pensionInfo) {
                    await tx.pensionInfo.upsert({
                        where: { participantId: id },
                        create: {
                            ...pensionInfo,
                            participantId: id,
                        },
                        update: pensionInfo,
                    })
                }

                // Update pension data if provided
                if (pensionData) {
                    const {
                        voluntaryContributions,
                        annualAccrual,
                        conversionDetails,
                        pensionParameters,
                        ...pensionDataFields
                    } = pensionData

                    await tx.pensionData.upsert({
                        where: { participantId: id },
                        create: {
                            ...pensionDataFields,
                            participantId: id,
                        },
                        update: pensionDataFields,
                    })

                    const pensionDataRecord = await tx.pensionData.findUnique({
                        where: { participantId: id },
                    })

                    if (pensionDataRecord) {
                        // Handle voluntary contributions
                        if (voluntaryContributions) {
                            await tx.voluntaryContribution.deleteMany({
                                where: { pensionDataId: pensionDataRecord.id },
                            })

                            if (voluntaryContributions.length > 0) {
                                await Promise.all(
                                    voluntaryContributions.map((contribution) =>
                                        tx.voluntaryContribution.create({
                                            data: {
                                                ...contribution,
                                                pensionDataId:
                                                    pensionDataRecord.id,
                                            },
                                        })
                                    )
                                )
                            }
                        }

                        // Handle annual accrual
                        if (annualAccrual) {
                            await tx.annualAccrual.upsert({
                                where: { pensionDataId: pensionDataRecord.id },
                                create: {
                                    ...annualAccrual,
                                    pensionDataId: pensionDataRecord.id,
                                },
                                update: annualAccrual,
                            })
                        }

                        // Handle conversion details
                        if (conversionDetails) {
                            await tx.conversionDetails.upsert({
                                where: { pensionDataId: pensionDataRecord.id },
                                create: {
                                    ...conversionDetails,
                                    pensionDataId: pensionDataRecord.id,
                                },
                                update: conversionDetails,
                            })
                        }
                    }
                }

                return updatedParticipant
            })

            return this.findOne(participant.id)
        } catch (error) {
            throw new BadRequestException(
                `Failed to update participant: ${error.message}`
            )
        }
    }

    async findAll(findAllParticipantsInput: FindAllParticipantsInput) {
        const { status, searchTerm, sortBy, sortOrder, skip, take } =
            findAllParticipantsInput

        const where: any = {}

        if (status) {
            where.status = status
        }

        if (searchTerm) {
            where.OR = [
                {
                    personalInfo: {
                        OR: [
                            {
                                firstName: {
                                    contains: searchTerm,
                                    mode: 'insensitive',
                                },
                            },
                            {
                                lastName: {
                                    contains: searchTerm,
                                    mode: 'insensitive',
                                },
                            },
                            {
                                email: {
                                    contains: searchTerm,
                                    mode: 'insensitive',
                                },
                            },
                        ],
                    },
                },
                {
                    employmentInfo: {
                        OR: [
                            {
                                employeeId: {
                                    contains: searchTerm,
                                    mode: 'insensitive',
                                },
                            },
                            {
                                department: {
                                    contains: searchTerm,
                                    mode: 'insensitive',
                                },
                            },
                            {
                                position: {
                                    contains: searchTerm,
                                    mode: 'insensitive',
                                },
                            },
                        ],
                    },
                },
            ]
        }

        const orderBy: any = {}
        if (sortBy) {
            orderBy[sortBy] = sortOrder || 'asc'
        } else {
            orderBy.createdAt = 'desc'
        }

        const participants = await this.prisma.participant.findMany({
            where,
            orderBy,
            skip: skip || 0,
            take: take || 50,
            include: this.getParticipantIncludes(),
        })

        const totalCount = await this.prisma.participant.count({ where })

        return {
            items: participants,
            totalCount,
        }
    }

    async findAllSlim() {
        const slimParticipant = await this.prisma.participant.findMany({
            select: {
                id: true,
                personalInfo: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        birthDay: true,
                        birthMonth: true,
                        birthYear: true,
                    },
                },
                pensionInfo: {
                    select: {
                        id: true,
                        code: true,
                        codeDescription: true,
                    },
                },
                status: true,
                lastModified: true,
            },
        })

        return slimParticipant
    }

    async findOne(id: string) {
        const participant = await this.prisma.participant.findUnique({
            where: { id },
            include: this.getParticipantIncludes(),
        })

        if (!participant) {
            throw new NotFoundException(`Participant with ID "${id}" not found`)
        }

        return participant
    }

    async remove(id: string, userId: string) {
        const participant = await this.findOne(id)

        const updatedParticipant = await this.prisma.participant.update({
            where: { id },
            data: {
                status: 'inactive',
                updatedAt: new Date(),
                updatedBy: userId,
            },
            include: this.getParticipantIncludes(),
        })

        await this.auditLogService.create({
            action: 'DELETE',
            userId,
            entityId: participant.id,
            entityType: 'PARTICIPANT',
            changes: {
                oldValue: { status: participant.status },
                newValue: { status: 'inactive' },
            },
        })

        return updatedParticipant
    }
}
