import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql'
import { UseGuards } from '@nestjs/common'
import { CertifiedIndexationStartOfYearService } from './certified-indexation-start-of-year.service'
import { CertifiedIndexationStartOfYear } from '../certified-data/entities/certified-indexation-start-of-year.entity'
import { CreateCertifiedIndexationStartOfYearInput } from '../certified-data/dto/create-certified-indexation-start-of-year.input'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'

@Resolver(() => CertifiedIndexationStartOfYear)
export class CertifiedIndexationStartOfYearResolver {
    constructor(
        private readonly certifiedIndexationService: CertifiedIndexationStartOfYearService
    ) {}

    @UseGuards(GraphqlAuthGuard)
    @Query(() => CertifiedIndexationStartOfYear)
    async certifiedIndexationStartOfYear(@Args('id') id: string) {
        return this.certifiedIndexationService.findOne(id)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => CertifiedIndexationStartOfYear)
    async certifiedIndexationByCertifiedDataId(
        @Args('certifiedDataId') certifiedDataId: string
    ) {
        return this.certifiedIndexationService.findByCertifiedDataId(
            certifiedDataId
        )
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => CertifiedIndexationStartOfYear)
    async createCertifiedIndexationStartOfYear(
        @Args('createCertifiedIndexationInput')
        createCertifiedIndexationInput: CreateCertifiedIndexationStartOfYearInput,
        @Args('certifiedDataId') certifiedDataId: string,
        @Context() context: any
    ) {
        return this.certifiedIndexationService.create(
            createCertifiedIndexationInput,
            certifiedDataId
        )
    }
}
