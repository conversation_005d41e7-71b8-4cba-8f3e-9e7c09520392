import { Module } from '@nestjs/common'
import { CertifiedIndexationStartOfYearService } from './certified-indexation-start-of-year.service'
import { CertifiedIndexationStartOfYearResolver } from './certified-indexation-start-of-year.resolver'
import { AuthModule } from '../auth/auth.module'

@Module({
    imports: [AuthModule],
    providers: [
        CertifiedIndexationStartOfYearResolver,
        CertifiedIndexationStartOfYearService,
    ],
    exports: [CertifiedIndexationStartOfYearService],
})
export class CertifiedIndexationStartOfYearModule {}

//CertifiedData:: 29d3c330-b50d-4225-b584-61182ae1644f
