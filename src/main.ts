import { NestFactory } from '@nestjs/core'
import { AppModule } from './app.module'
import { BadRequestException, ValidationPipe } from '@nestjs/common'
import { GraphQLErrorFilter } from './filters/custom-exception.filter'
import { ConfigService } from '@nestjs/config'

async function bootstrap() {
    const app = await NestFactory.create(AppModule, {
        bodyParser: false,
        rawBody: true,
    })
    const configService = app.get(ConfigService)
    app.enableCors({
        origin: '*', //TODO: change to frontend url
        credentials: true,
        allowedHeaders: [
            'Accept',
            'Authorization',
            'Content-Type',
            'X-Requested-With',
            'apollo-require-preflight',
        ],
        methods: ['GET', 'PUT', 'POST', 'DELETE', 'OPTIONS'],
    })
    app.useGlobalPipes(
        new ValidationPipe({
            whitelist: true,
            transform: true,
            exceptionFactory: (errors) => {
                const formattedErrors = errors.reduce((accumulator, error) => {
                    accumulator[error.property] = Object.values(
                        error.constraints
                    ).join(', ')
                    return accumulator
                }, {})
                throw new BadRequestException(formattedErrors)
            },
        })
    )
    app.useGlobalFilters(new GraphQLErrorFilter())
    await app.listen(parseInt(configService.get('GLOBAL.PORT')) || 3500)
}

bootstrap()
