// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl", "linux-musl-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                         String               @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  firebaseUid                String               @unique
  createdAt                  DateTime             @default(now())
  updatedAt                  DateTime             @updatedAt
  email                      String               @unique
  firstname                  String?
  lastname                   String?
  lastLogin                  DateTime?
  role                       Role                 @relation(fields: [roleId], references: [id])
  systemSettings             SystemSettings[]
  pensionParameters          PensionParameters[]
  roleId                     String               @db.Uuid
  createdPensionCorrections  PensionCorrections[] @relation("CreatedByUser")
  reviewedPensionCorrections PensionCorrections[] @relation("ReviewedByUser")
  createdChangeProposals     ChangeProposal[]     @relation("CreatedByUser")
  reviewedChangeProposals    ChangeProposal[]     @relation("ReviewedByUser")
  auditLogs                  AuditLog[]
  certifiedData              CertifiedData[]
  receivedNotifications      Notification[]       @relation("Recipient")
  sentNotifications          Notification[]       @relation("CreatedBy")
}

model PensionCorrections {
  id           String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  correction   Float
  createdAt    DateTime @default(now())
  createdBy    User     @relation(name: "CreatedByUser", fields: [createdById], references: [id])
  year         String
  reviewedAt   DateTime
  reviewedBy   User     @relation(name: "ReviewedByUser", fields: [reviewedById], references: [id])
  createdById  String   @db.Uuid
  reviewedById String   @db.Uuid
  updatedAt    DateTime @updatedAt
}

model SystemSettings {
  id                   String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  autoApproveChanges   Boolean  @default(false)
  effectiveDate        DateTime
  passwordExpiryDays   Int      @default(90)
  requireTwoFactorAuth Boolean  @default(true)
  sessionTimeout       Int      @default(30)
  updatedBy            User     @relation(fields: [userId], references: [id])
  updatedAt            DateTime @updatedAt
  createdAt            DateTime @default(now())
  userId               String   @db.Uuid
}

model PensionParameters {
  id                                String       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  accrualPercentage                 Float        @default(0.02)
  annualMultiplier                  Float        @default(13)
  offsetAmount                      Float        @default(17616)
  partnersPensionPercentage         Float        @default(0.70)
  retirementAge                     Int          @default(65)
  voluntaryContributionInterestRate Float        @default(0.04)
  year                              String
  pensionDataId                     String       @db.Uuid
  effectiveDate                     DateTime     @default(now())
  updatedAt                         DateTime     @updatedAt
  updatedBy                         User         @relation(fields: [userId], references: [id])
  createdAt                         DateTime     @default(now())
  userId                            String       @db.Uuid
  pendingChanges                    String[]
  pensionData                       PensionData? @relation(fields: [pensionDataId], references: [id])
}

model ChangeProposal {
  id                      String       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  createdAt               DateTime     @default(now())
  createdBy               User         @relation(name: "CreatedByUser", fields: [createdById], references: [id])
  createdById             String       @db.Uuid
  effectiveDate           DateTime
  entityId                String?
  entityType              String
  type                    ChangeType?  @default(PARTICIPANT)
  participantName         String?
  status                  ChangeStatus @default(PENDING)
  reviewComments          String?
  reviewedAt              DateTime?
  reviewedBy              User?        @relation(name: "ReviewedByUser", fields: [reviewedById], references: [id])
  reviewedById            String?      @db.Uuid
  updatedAt               DateTime     @updatedAt
  isCertificationProposal Boolean      @default(false)
  changePropagated        Boolean      @default(false)
  changes                 ChangeData[]
}

model ChangeData {
  id               String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  changeProposal   ChangeProposal @relation(fields: [changeProposalId], references: [id], onDelete: Cascade)
  changeProposalId String         @db.Uuid
  path             String
  newValue         String
  oldValue         String?
  AuditLog         AuditLog[]

  @@index([changeProposalId])
}

model Participant {
  id             String          @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  createdAt      DateTime        @default(now())
  createdBy      String
  status         String          @default("active")
  approvalStatus ApprovalStatus @default(PENDING)
  updatedAt      DateTime        @updatedAt
  updatedBy      String
  lastModified   DateTime?
  personalInfo   PersonalInfo?
  documents      Document[]
  employmentInfo EmploymentInfo?
  pensionInfo    PensionInfo?
  pensionData    PensionData?
  certifiedData  CertifiedData[]
}

model PersonalInfo {
  id             String        @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  participant    Participant   @relation(fields: [participantId], references: [id], onDelete: Cascade)
  participantId  String        @unique @db.Uuid
  firstName      String
  lastName       String
  email          String?
  phone          String?
  maritalStatus  String?
  birthDay       Int?
  birthMonth     Int?
  birthYear      Int?
  address        Address?
  partnerInfo    PartnerInfo[]
  children       Child[]
  pendingChanges String[]
}

model Address {
  id             String       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  personalInfo   PersonalInfo @relation(fields: [personalInfoId], references: [id], onDelete: Cascade)
  personalInfoId String       @unique @db.Uuid
  street         String?
  houseNumber    String?
  postalCode     String?
  city           String?
  state          String?
  country        String?
  pendingChanges String[]
}

model PartnerInfo {
  id              String       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  personalInfo    PersonalInfo @relation(fields: [personalInfoId], references: [id], onDelete: Cascade)
  personalInfoId  String       @db.Uuid
  isCurrent       Boolean
  firstName       String?
  lastName        String?
  dateOfBirth     DateTime?
  isDeceased      Boolean      @default(false)
  startDate       DateTime?
  code            Int?
  previousCode    Int?
  codeDescription String?
  pendingChanges  String[]
}

model Child {
  id              String       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  personalInfo    PersonalInfo @relation(fields: [personalInfoId], references: [id])
  personalInfoId  String       @db.Uuid
  firstName       String?
  lastName        String?
  dateOfBirth     DateTime?
  isOrphan        Boolean      @default(false)
  isStudying      Boolean      @default(false)
  code            Int?
  previousCode    Int?
  codeDescription String?
  pendingChanges  String[]

  @@index([personalInfoId])
}

model Document {
  id            String      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  participant   Participant @relation(fields: [participantId], references: [id])
  participantId String      @db.Uuid
  documentId    String?
  name          String?
  type          String?
  mimeType      String?
  path          String?
  size          Int?
  createdBy     String?
  uploadedAt    DateTime?

  @@index([participantId])
}

model EmploymentInfo {
  id             String        @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  participant    Participant   @relation(fields: [participantId], references: [id], onDelete: Cascade)
  participantId  String        @unique @db.Uuid
  employeeId     String?
  department     String?
  position       String?
  regNum         Int?
  havNum         Int?
  startDate      DateTime?
  endDate        DateTime?
  status         String?
  salaryEntries  SalaryEntry[]
  pendingChanges String[]
}

model SalaryEntry {
  id                 String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  employmentInfo     EmploymentInfo @relation(fields: [employmentInfoId], references: [id])
  employmentInfoId   String         @db.Uuid
  year               Int
  amount             Float
  partTimePercentage Float
  pendingChanges     String[]

  @@unique([employmentInfoId, year])
  @@index([employmentInfoId])
}

model PensionInfo {
  id                                     String      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  participant                            Participant @relation(fields: [participantId], references: [id], onDelete: Cascade)
  participantId                          String      @unique @db.Uuid
  code                                   Int?
  previousCode                           Int?
  codeEffectiveDate                      DateTime?
  previousCodeEffectiveDate              DateTime?
  codeDescription                        String?
  codeImpact                             String?
  accruedGrossAnnualOldAgePension        Float?
  accruedGrossAnnualPartnersPension      Float?
  accruedGrossAnnualSinglesPension       Float?
  attainableGrossAnnualOldAgePension     Float?
  extraAccruedGrossAnnualOldAgePension   Float?
  extraAccruedGrossAnnualPartnersPension Float?
  grossAnnualDisabilityPension           Float?
  pensionBase                            Float?
  pendingChanges                         String[]
}

model PensionData {
  id                     String                  @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  participant            Participant             @relation(fields: [participantId], references: [id], onDelete: Cascade)
  participantId          String                  @unique @db.Uuid
  status                 String?
  retirementDate         DateTime?
  pensionableAmount      Float?
  totalContributions     Float?
  voluntaryContributions VoluntaryContribution[]
  annualAccrual          AnnualAccrual?
  conversionDetails      ConversionDetails?
  pendingChanges         String[]
  pensionParameters      PensionParameters[]
}

model AnnualAccrual {
  id                    String      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  pensionData           PensionData @relation(fields: [pensionDataId], references: [id], onDelete: Cascade)
  pensionDataId         String      @unique @db.Uuid
  employeeContributions Float?
  employerContributions Float?
  franchise             Float?
  monthlyBenefit        Float?
  pendingChanges        String[]
}

model VoluntaryContribution {
  id                  String      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  pensionData         PensionData @relation(fields: [pensionDataId], references: [id])
  pensionDataId       String      @db.Uuid
  amount              Float?
  accumulatedInterest Float?
  date                DateTime?
  type                String?
  pendingChanges      String[]

  @@index([pensionDataId])
}

model ConversionDetails {
  id                     String      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  pensionData            PensionData @relation(fields: [pensionDataId], references: [id], onDelete: Cascade)
  pensionDataId          String      @unique @db.Uuid
  conversionDate         DateTime?
  conversionRate         Float?
  oldAgePensionIncrease  Float?
  partnerPensionIncrease Float?
  pendingChanges         String[]
}

model AuditLog {
  id           String     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  timestamp    DateTime   @default(now())
  action       String
  userId       String     @db.Uuid
  userRole     String?
  entityId     String     @db.Uuid
  entityType   String
  proposalId   String?    @db.Uuid
  changes      ChangeData @relation(fields: [changeDataId], references: [id])
  changeDataId String     @db.Uuid
  user         User?      @relation(fields: [userId], references: [id])
  ipAddress    String?
  userAgent    String?

  @@index([timestamp])
  @@index([userId])
  @@index([entityId, entityType])
  @@index([proposalId])
}

model CertifiedData {
  id                              String                           @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  participantId                   String                           @db.Uuid
  participant                     Participant                      @relation(fields: [participantId], references: [id])
  certificationYear               Int
  certificationStatus             String?
  certifiedAt                     DateTime
  certifiedBy                     User                             @relation(fields: [certifiedById], references: [id])
  certifiedById                   String                           @db.Uuid
  certifiedPensionInfo            CertifiedPensionInfo?
  certifiedEmploymentInfo         CertifiedEmploymentInfo?
  certifiedPersonalInfo           CertifiedPersonalInfo?
  certifiedIndexationStartOfYear  CertifiedIndexationStartOfYear?
  certifiedPensionCorrections     CertifiedPensionCorrections?
  certifiedVoluntaryContributions CertifiedVoluntaryContributions?
  certifiedPensionParameters      CertifiedPensionParameters?
  certifiedChild                  CertifiedChild[]
  notes                           String?
  certificationRejectReason       CertificationRejectReason[]
  certifiedPartnerInfo            CertifiedPartnerInfo[]
  certifiedAddress                CertifiedAddress?

  @@unique([participantId, certificationYear])
  @@index([participantId])
  @@index([certificationYear])
}

model CertifiedPensionInfo {
  id                                     String                      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  certifiedData                          CertifiedData               @relation(fields: [certifiedDataId], references: [id], onDelete: Cascade)
  certifiedDataId                        String                      @unique @db.Uuid
  code                                   Int?
  codeDescription                        String?
  accruedGrossAnnualOldAgePension        Float?
  accruedGrossAnnualPartnersPension      Float?
  accruedGrossAnnualSinglesPension       Float?
  attainableGrossAnnualOldAgePension     Float?
  extraAccruedGrossAnnualOldAgePension   Float?
  extraAccruedGrossAnnualPartnersPension Float?
  grossAnnualDisabilityPension           Float?
  pensionBase                            Float?
  pendingChanges                         String[]
  requestedChanges                       String[]
  approvedChanges                        String[]
  certificationRejectReason              CertificationRejectReason[]
}

model CertifiedSalaryEntry {
  id                        String                      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  certifiedEmploymentInfo   CertifiedEmploymentInfo     @relation(fields: [certifiedEmploymentInfoId], references: [id])
  certifiedEmploymentInfoId String                      @db.Uuid
  year                      Int
  amount                    Float
  partTimePercentage        Float
  pendingChanges            String[]
  requestedChanges          String[]
  approvedChanges           String[]
  certificationRejectReason CertificationRejectReason[]

  @@unique([certifiedEmploymentInfoId, year])
  @@index([certifiedEmploymentInfoId])
}

model CertifiedEmploymentInfo {
  id                        String                      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  certifiedData             CertifiedData               @relation(fields: [certifiedDataId], references: [id], onDelete: Cascade)
  certifiedDataId           String                      @unique @db.Uuid
  employeeId                String?
  department                String?
  position                  String?
  regNum                    Int?
  havNum                    Int?
  startDate                 DateTime?
  status                    String?
  certifiedSalaryEntries    CertifiedSalaryEntry[]
  pendingChanges            String[]
  requestedChanges          String[]
  approvedChanges           String[]
  certificationRejectReason CertificationRejectReason[]
}

model CertifiedPersonalInfo {
  id                        String                      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  certifiedData             CertifiedData               @relation(fields: [certifiedDataId], references: [id], onDelete: Cascade)
  certifiedDataId           String                      @unique @db.Uuid
  firstName                 String?
  lastName                  String?
  email                     String?
  phone                     String?
  maritalStatus             String?
  birthDay                  Int?
  birthMonth                Int?
  birthYear                 Int?
  address                   Json?
  partnerInfo               Json?
  children                  Json?
  pendingChanges            String[]
  requestedChanges          String[]
  approvedChanges           String[]
  certificationRejectReason CertificationRejectReason[]
}

model CertifiedPartnerInfo {
  id                        String                      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  certifiedData             CertifiedData               @relation(fields: [certifiedDataId], references: [id], onDelete: Cascade)
  certifiedDataId           String                      @db.Uuid
  firstName                 String?
  lastName                  String?
  dateOfBirth               DateTime?
  isDeceased                Boolean                     @default(false)
  startDate                 DateTime?
  isCurrent                 Boolean
  pendingChanges            String[]
  requestedChanges          String[]
  approvedChanges           String[]
  certificationRejectReason CertificationRejectReason[]
}

model CertifiedChild {
  id                        String                      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  certifiedData             CertifiedData               @relation(fields: [certifiedDataId], references: [id], onDelete: Cascade)
  certifiedDataId           String                      @db.Uuid
  firstName                 String?
  lastName                  String?
  dateOfBirth               DateTime?
  isOrphan                  Boolean                     @default(false)
  isStudying                Boolean                     @default(false)
  pendingChanges            String[]
  requestedChanges          String[]
  approvedChanges           String[]
  certificationRejectReason CertificationRejectReason[]
}

model CertifiedIndexationStartOfYear {
  id                                     String                      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  certifiedData                          CertifiedData               @relation(fields: [certifiedDataId], references: [id], onDelete: Cascade)
  certifiedDataId                        String                      @unique @db.Uuid
  accruedGrossAnnualOldAgePension        Float?
  accruedGrossAnnualPartnersPension      Float?
  accruedGrossAnnualSinglesPension       Float?
  extraAccruedGrossAnnualOldAgePension   Float?
  extraAccruedGrossAnnualPartnersPension Float?
  grossAnnualDisabilityPension           Float?
  pendingChanges                         String[]
  requestedChanges                       String[]
  approvedChanges                        String[]
  certificationRejectReason              CertificationRejectReason[]
}

model CertifiedPensionCorrections {
  id                                     String                      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  certifiedData                          CertifiedData               @relation(fields: [certifiedDataId], references: [id], onDelete: Cascade)
  certifiedDataId                        String                      @unique @db.Uuid
  accruedGrossAnnualOldAgePension        Float?
  attainableGrossAnnualOldAgePension     Float?
  accruedGrossAnnualPartnersPension      Float?
  accruedGrossAnnualSinglesPension       Float?
  grossAnnualDisabilityPension           Float?
  extraAccruedGrossAnnualOldAgePension   Float?
  extraAccruedGrossAnnualPartnersPension Float?
  correction                             Float?
  year                                   String?
  pendingChanges                         String[]
  requestedChanges                       String[]
  approvedChanges                        String[]
  certificationRejectReason              CertificationRejectReason[]
}

model CertifiedVoluntaryContributions {
  id                        String                      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  certifiedData             CertifiedData               @relation(fields: [certifiedDataId], references: [id], onDelete: Cascade)
  certifiedDataId           String                      @unique @db.Uuid
  contributions             Json?
  pendingChanges            String[]
  requestedChanges          String[]
  approvedChanges           String[]
  certificationRejectReason CertificationRejectReason[]
}

model CertifiedPensionParameters {
  id                                String                      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  certifiedData                     CertifiedData               @relation(fields: [certifiedDataId], references: [id], onDelete: Cascade)
  certifiedDataId                   String                      @unique @db.Uuid
  accrualPercentage                 Float?
  annualMultiplier                  Float?
  offsetAmount                      Float?
  partnersPensionPercentage         Float?
  retirementAge                     Int?
  voluntaryContributionInterestRate Float?
  year                              String?
  effectiveDate                     DateTime?
  pendingChanges                    String[]
  requestedChanges                  String[]
  approvedChanges                   String[]
  certificationRejectReason         CertificationRejectReason[]
}

model CertifiedAddress {
  id                        String                      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  certifiedData             CertifiedData               @relation(fields: [certifiedDataId], references: [id], onDelete: Cascade)
  certifiedDataId           String                      @unique @db.Uuid
  street                    String?
  houseNumber               String?
  postalCode                String?
  city                      String?
  state                     String?
  country                   String?
  pendingChanges            String[]
  requestedChanges          String[]
  approvedChanges           String[]
  certificationRejectReason CertificationRejectReason[]
}

model Role {
  id             String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name           String   @unique
  description    String?
  users          User[]
  pendingChanges String[]
}

model Notification {
  id          String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  createdAt   DateTime  @default(now())
  createdBy   User      @relation(name: "CreatedBy", fields: [createdById], references: [id])
  createdById String    @db.Uuid
  message     String
  recipient   User      @relation(name: "Recipient", fields: [recipientId], references: [id])
  recipientId String    @db.Uuid
  read        Boolean   @default(false)
  readAt      DateTime?
  type        String
  entityId    String
  entityType  String
}

model CertificationRejectReason {
  id                                String                           @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  field                             String
  reason                            String
  status                            CertificationRejectReasonStatus  @default(VALID)
  createdAt                         DateTime                         @default(now())
  updatedAt                         DateTime                         @updatedAt
  certifiedData                     CertifiedData?                   @relation(fields: [certifiedDataId], references: [id])
  certifiedDataId                   String?                          @db.Uuid
  certifiedPensionInfo              CertifiedPensionInfo?            @relation(fields: [certifiedPensionInfoId], references: [id])
  certifiedPensionInfoId            String?                          @db.Uuid
  certifiedSalaryEntry              CertifiedSalaryEntry?            @relation(fields: [certifiedSalaryEntryId], references: [id])
  certifiedSalaryEntryId            String?                          @db.Uuid
  certifiedEmploymentInfo           CertifiedEmploymentInfo?         @relation(fields: [certifiedEmploymentInfoId], references: [id])
  certifiedEmploymentInfoId         String?                          @db.Uuid
  certifiedPersonalInfo             CertifiedPersonalInfo?           @relation(fields: [certifiedPersonalInfoId], references: [id])
  certifiedPersonalInfoId           String?                          @db.Uuid
  certifiedChild                    CertifiedChild?                  @relation(fields: [certifiedChildId], references: [id])
  certifiedChildId                  String?                          @db.Uuid
  certifiedIndexationStartOfYear    CertifiedIndexationStartOfYear?  @relation(fields: [certifiedIndexationStartOfYearId], references: [id])
  certifiedIndexationStartOfYearId  String?                          @db.Uuid
  certifiedPensionCorrections       CertifiedPensionCorrections?     @relation(fields: [certifiedPensionCorrectionsId], references: [id])
  certifiedPensionCorrectionsId     String?                          @db.Uuid
  certifiedVoluntaryContributions   CertifiedVoluntaryContributions? @relation(fields: [certifiedVoluntaryContributionsId], references: [id])
  certifiedVoluntaryContributionsId String?                          @db.Uuid
  certifiedPensionParameters        CertifiedPensionParameters?      @relation(fields: [certifiedPensionParametersId], references: [id])
  certifiedPensionParametersId      String?                          @db.Uuid
  certifiedPartnerInfo              CertifiedPartnerInfo?            @relation(fields: [certifiedPartnerInfoId], references: [id])
  certifiedPartnerInfoId            String?                          @db.Uuid
  certifiedAddress                  CertifiedAddress?                @relation(fields: [certifiedAddressId], references: [id])
  certifiedAddressId                String?                          @db.Uuid
}

enum ChangeStatus {
  PENDING
  APPROVED
  REJECTED
}

enum ApprovalStatus {
  PENDING
  APPROVED
  REJECTED
}

enum ChangeType {
  PARTICIPANT
  PARAMETERS
  SALARY
  CERTIFIED_DATA
}

enum CertificationRejectReasonStatus {
  VALID
  INVALID
}
