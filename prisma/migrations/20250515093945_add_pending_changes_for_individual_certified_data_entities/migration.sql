-- AlterTable
ALTER TABLE "CertifiedEmploymentInfo" ADD COLUMN     "pendingChanges" TEXT[];

-- AlterTable
ALTER TABLE "CertifiedIndexationStartOfYear" ADD COLUMN     "pendingChanges" TEXT[];

-- AlterTable
ALTER TABLE "CertifiedPensionCorrections" ADD COLUMN     "pendingChanges" TEXT[];

-- AlterTable
ALTER TABLE "CertifiedPensionInfo" ADD COLUMN     "pendingChanges" TEXT[];

-- AlterTable
ALTER TABLE "CertifiedPensionParameters" ADD COLUMN     "pendingChanges" TEXT[];

-- AlterTable
ALTER TABLE "CertifiedPersonalInfo" ADD COLUMN     "pendingChanges" TEXT[];

-- AlterTable
ALTER TABLE "CertifiedVoluntaryContributions" ADD COLUMN     "pendingChanges" TEXT[];

-- AlterTable
ALTER TABLE "ChangeProposal" ADD COLUMN     "isCertificationProposal" BOOLEAN NOT NULL DEFAULT false;
