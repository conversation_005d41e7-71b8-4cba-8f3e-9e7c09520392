/*
  Warnings:

  - You are about to drop the column `rejectedChanges` on the `CertifiedEmploymentInfo` table. All the data in the column will be lost.
  - You are about to drop the column `rejectedChanges` on the `CertifiedIndexationStartOfYear` table. All the data in the column will be lost.
  - You are about to drop the column `rejectedChanges` on the `CertifiedPensionCorrections` table. All the data in the column will be lost.
  - You are about to drop the column `rejectedChanges` on the `CertifiedPensionInfo` table. All the data in the column will be lost.
  - You are about to drop the column `rejectedChanges` on the `CertifiedPensionParameters` table. All the data in the column will be lost.
  - You are about to drop the column `rejectedChanges` on the `CertifiedPersonalInfo` table. All the data in the column will be lost.
  - You are about to drop the column `rejectedChanges` on the `CertifiedSalaryEntry` table. All the data in the column will be lost.
  - You are about to drop the column `rejectedChanges` on the `CertifiedVoluntaryContributions` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "CertifiedEmploymentInfo" DROP COLUMN "rejectedChanges";

-- AlterTable
ALTER TABLE "CertifiedIndexationStartOfYear" DROP COLUMN "rejectedChanges";

-- AlterTable
ALTER TABLE "CertifiedPensionCorrections" DROP COLUMN "rejectedChanges";

-- AlterTable
ALTER TABLE "CertifiedPensionInfo" DROP COLUMN "rejectedChanges";

-- AlterTable
ALTER TABLE "CertifiedPensionParameters" DROP COLUMN "rejectedChanges";

-- AlterTable
ALTER TABLE "CertifiedPersonalInfo" DROP COLUMN "rejectedChanges";

-- AlterTable
ALTER TABLE "CertifiedSalaryEntry" DROP COLUMN "rejectedChanges";

-- AlterTable
ALTER TABLE "CertifiedVoluntaryContributions" DROP COLUMN "rejectedChanges";
