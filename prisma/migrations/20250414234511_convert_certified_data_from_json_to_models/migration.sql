/*
  Warnings:

  - You are about to drop the column `employmentInfo` on the `CertifiedData` table. All the data in the column will be lost.
  - You are about to drop the column `indexationStartOfYear` on the `CertifiedData` table. All the data in the column will be lost.
  - You are about to drop the column `pensionCorrectionsStartOfYear` on the `CertifiedData` table. All the data in the column will be lost.
  - You are about to drop the column `pensionInfo` on the `CertifiedData` table. All the data in the column will be lost.
  - You are about to drop the column `pensionParameters` on the `CertifiedData` table. All the data in the column will be lost.
  - You are about to drop the column `personalInfo` on the `CertifiedData` table. All the data in the column will be lost.
  - You are about to drop the column `voluntaryAdditionalContributions` on the `CertifiedData` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "CertifiedData" DROP COLUMN "employmentInfo",
DROP COLUMN "indexationStartOfYear",
DROP COLUMN "pensionCorrectionsStartOfYear",
DROP COLUMN "pensionInfo",
DROP COLUMN "pensionParameters",
DROP COLUMN "personalInfo",
DROP COLUMN "voluntaryAdditionalContributions";

-- CreateTable
CREATE TABLE "CertifiedPensionInfo" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedDataId" UUID NOT NULL,
    "code" INTEGER,
    "codeDescription" TEXT,

    CONSTRAINT "CertifiedPensionInfo_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CertifiedEmploymentInfo" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedDataId" UUID NOT NULL,
    "employeeId" TEXT,
    "department" TEXT,
    "position" TEXT,
    "regNum" INTEGER,
    "havNum" INTEGER,
    "startDate" TIMESTAMP(3),
    "status" TEXT,
    "salaryEntries" JSONB,

    CONSTRAINT "CertifiedEmploymentInfo_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CertifiedPersonalInfo" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedDataId" UUID NOT NULL,
    "firstName" TEXT,
    "lastName" TEXT,
    "email" TEXT,
    "phone" TEXT,
    "maritalStatus" TEXT,
    "birthDay" INTEGER,
    "birthMonth" INTEGER,
    "birthYear" INTEGER,
    "address" JSONB,
    "partnerInfo" JSONB,
    "children" JSONB,

    CONSTRAINT "CertifiedPersonalInfo_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CertifiedIndexationStartOfYear" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedDataId" UUID NOT NULL,
    "accruedGrossAnnualOldAgePension" DOUBLE PRECISION,
    "accruedGrossAnnualPartnersPension" DOUBLE PRECISION,
    "accruedGrossAnnualSinglesPension" DOUBLE PRECISION,
    "extraAccruedGrossAnnualOldAgePension" DOUBLE PRECISION,
    "extraAccruedGrossAnnualPartnersPension" DOUBLE PRECISION,
    "grossAnnualDisabilityPension" DOUBLE PRECISION,

    CONSTRAINT "CertifiedIndexationStartOfYear_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CertifiedPensionCorrections" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedDataId" UUID NOT NULL,
    "accruedGrossAnnualOldAgePension" DOUBLE PRECISION,
    "attainableGrossAnnualOldAgePension" DOUBLE PRECISION,
    "accruedGrossAnnualPartnersPension" DOUBLE PRECISION,
    "accruedGrossAnnualSinglesPension" DOUBLE PRECISION,
    "grossAnnualDisabilityPension" DOUBLE PRECISION,
    "correction" DOUBLE PRECISION,
    "year" TEXT,

    CONSTRAINT "CertifiedPensionCorrections_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CertifiedVoluntaryContributions" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedDataId" UUID NOT NULL,
    "contributions" JSONB,

    CONSTRAINT "CertifiedVoluntaryContributions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CertifiedPensionParameters" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedDataId" UUID NOT NULL,
    "accrualPercentage" DOUBLE PRECISION,
    "annualMultiplier" DOUBLE PRECISION,
    "offsetAmount" DOUBLE PRECISION,
    "partnersPensionPercentage" DOUBLE PRECISION,
    "retirementAge" INTEGER,
    "voluntaryContributionInterestRate" DOUBLE PRECISION,
    "year" TEXT,
    "effectiveDate" TIMESTAMP(3),

    CONSTRAINT "CertifiedPensionParameters_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedPensionInfo_certifiedDataId_key" ON "CertifiedPensionInfo"("certifiedDataId");

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedEmploymentInfo_certifiedDataId_key" ON "CertifiedEmploymentInfo"("certifiedDataId");

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedPersonalInfo_certifiedDataId_key" ON "CertifiedPersonalInfo"("certifiedDataId");

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedIndexationStartOfYear_certifiedDataId_key" ON "CertifiedIndexationStartOfYear"("certifiedDataId");

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedPensionCorrections_certifiedDataId_key" ON "CertifiedPensionCorrections"("certifiedDataId");

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedVoluntaryContributions_certifiedDataId_key" ON "CertifiedVoluntaryContributions"("certifiedDataId");

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedPensionParameters_certifiedDataId_key" ON "CertifiedPensionParameters"("certifiedDataId");

-- AddForeignKey
ALTER TABLE "CertifiedPensionInfo" ADD CONSTRAINT "CertifiedPensionInfo_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertifiedEmploymentInfo" ADD CONSTRAINT "CertifiedEmploymentInfo_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertifiedPersonalInfo" ADD CONSTRAINT "CertifiedPersonalInfo_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertifiedIndexationStartOfYear" ADD CONSTRAINT "CertifiedIndexationStartOfYear_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertifiedPensionCorrections" ADD CONSTRAINT "CertifiedPensionCorrections_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertifiedVoluntaryContributions" ADD CONSTRAINT "CertifiedVoluntaryContributions_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertifiedPensionParameters" ADD CONSTRAINT "CertifiedPensionParameters_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE CASCADE ON UPDATE CASCADE;
