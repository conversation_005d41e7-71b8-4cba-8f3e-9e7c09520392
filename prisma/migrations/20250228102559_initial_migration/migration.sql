-- CreateEnum
CREATE TYPE "ChangeStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED');

-- CreateTable
CREATE TABLE "User" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "firebaseUid" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "email" TEXT NOT NULL,
    "firstname" TEXT,
    "lastname" TEXT,
    "lastLogin" TIMESTAMP(3),
    "roleId" UUID NOT NULL,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PensionCorrections" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "correction" DOUBLE PRECISION NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "year" TEXT NOT NULL,
    "reviewedAt" TIMESTAMP(3) NOT NULL,
    "createdById" UUID NOT NULL,
    "reviewedById" UUID NOT NULL,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PensionCorrections_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SystemSettings" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "autoApproveChanges" BOOLEAN NOT NULL DEFAULT false,
    "effectiveDate" TIMESTAMP(3) NOT NULL,
    "passwordExpiryDays" INTEGER NOT NULL DEFAULT 90,
    "requireTwoFactorAuth" BOOLEAN NOT NULL DEFAULT true,
    "sessionTimeout" INTEGER NOT NULL DEFAULT 30,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "userId" UUID NOT NULL,

    CONSTRAINT "SystemSettings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PensionParameters" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "accrualPercentage" DOUBLE PRECISION NOT NULL DEFAULT 0.02,
    "annualMultiplier" DOUBLE PRECISION NOT NULL DEFAULT 13,
    "offsetAmount" DOUBLE PRECISION NOT NULL DEFAULT 17616,
    "partnersPensionPercentage" DOUBLE PRECISION NOT NULL DEFAULT 0.70,
    "retirementAge" INTEGER NOT NULL DEFAULT 65,
    "voluntaryContributionInterestRate" DOUBLE PRECISION NOT NULL DEFAULT 0.04,
    "year" TEXT NOT NULL,
    "pensionDataId" UUID NOT NULL,
    "effectiveDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "userId" UUID NOT NULL,

    CONSTRAINT "PensionParameters_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ChangeProposal" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdById" UUID NOT NULL,
    "effectiveDate" TIMESTAMP(3) NOT NULL,
    "entityId" UUID NOT NULL,
    "entityType" TEXT NOT NULL,
    "status" "ChangeStatus" NOT NULL DEFAULT 'PENDING',
    "reviewComments" TEXT,
    "reviewedAt" TIMESTAMP(3),
    "reviewedById" UUID,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ChangeProposal_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ChangeData" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "changeProposalId" UUID NOT NULL,
    "path" TEXT NOT NULL,
    "newValue" JSONB NOT NULL,
    "oldValue" JSONB,

    CONSTRAINT "ChangeData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Participant" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'active',
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "updatedBy" TEXT NOT NULL,
    "lastModified" TIMESTAMP(3),

    CONSTRAINT "Participant_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PersonalInfo" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "participantId" UUID NOT NULL,
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "email" TEXT,
    "phone" TEXT,
    "maritalStatus" TEXT,
    "birthDay" INTEGER,
    "birthMonth" INTEGER,
    "birthYear" INTEGER,

    CONSTRAINT "PersonalInfo_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Address" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "personalInfoId" UUID NOT NULL,
    "street" TEXT,
    "houseNumber" TEXT,
    "postalCode" TEXT,
    "city" TEXT,
    "state" TEXT,
    "country" TEXT,

    CONSTRAINT "Address_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PartnerInfo" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "personalInfoId" UUID NOT NULL,
    "firstName" TEXT,
    "lastName" TEXT,
    "dateOfBirth" TIMESTAMP(3),
    "isDeceased" BOOLEAN NOT NULL DEFAULT false,
    "startDate" TIMESTAMP(3),

    CONSTRAINT "PartnerInfo_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Child" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "personalInfoId" UUID NOT NULL,
    "firstName" TEXT,
    "lastName" TEXT,
    "dateOfBirth" TIMESTAMP(3),
    "isOrphan" BOOLEAN NOT NULL DEFAULT false,
    "isStudying" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Child_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Document" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "participantId" UUID NOT NULL,
    "documentId" TEXT,
    "name" TEXT,
    "type" TEXT,
    "mimeType" TEXT,
    "path" TEXT,
    "size" INTEGER,
    "createdBy" TEXT,
    "uploadedAt" TIMESTAMP(3),

    CONSTRAINT "Document_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EmploymentInfo" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "participantId" UUID NOT NULL,
    "employeeId" TEXT,
    "department" TEXT,
    "position" TEXT,
    "regNum" INTEGER,
    "havNum" INTEGER,
    "startDate" TIMESTAMP(3),
    "status" TEXT,

    CONSTRAINT "EmploymentInfo_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SalaryEntry" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "employmentInfoId" UUID NOT NULL,
    "year" INTEGER NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "partTimePercentage" DOUBLE PRECISION NOT NULL,

    CONSTRAINT "SalaryEntry_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PensionInfo" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "participantId" UUID NOT NULL,
    "code" INTEGER,
    "codeDescription" TEXT,

    CONSTRAINT "PensionInfo_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PensionData" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "participantId" UUID NOT NULL,
    "status" TEXT,
    "retirementDate" TIMESTAMP(3),
    "pensionableAmount" DOUBLE PRECISION,
    "totalContributions" DOUBLE PRECISION,

    CONSTRAINT "PensionData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AnnualAccrual" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "pensionDataId" UUID NOT NULL,
    "employeeContributions" DOUBLE PRECISION,
    "employerContributions" DOUBLE PRECISION,
    "franchise" DOUBLE PRECISION,
    "monthlyBenefit" DOUBLE PRECISION,

    CONSTRAINT "AnnualAccrual_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "VoluntaryContribution" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "pensionDataId" UUID NOT NULL,
    "amount" DOUBLE PRECISION,
    "accumulatedInterest" DOUBLE PRECISION,
    "date" TIMESTAMP(3),
    "type" TEXT,

    CONSTRAINT "VoluntaryContribution_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ConversionDetails" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "pensionDataId" UUID NOT NULL,
    "conversionDate" TIMESTAMP(3),
    "conversionRate" DOUBLE PRECISION,
    "oldAgePensionIncrease" DOUBLE PRECISION,
    "partnerPensionIncrease" DOUBLE PRECISION,

    CONSTRAINT "ConversionDetails_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AuditLog" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "action" TEXT NOT NULL,
    "userId" UUID NOT NULL,
    "userRole" TEXT,
    "entityId" UUID NOT NULL,
    "entityType" TEXT NOT NULL,
    "proposalId" UUID,
    "changes" JSONB,
    "ipAddress" TEXT,
    "userAgent" TEXT,

    CONSTRAINT "AuditLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CertifiedData" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "participantId" UUID NOT NULL,
    "certificationYear" INTEGER NOT NULL,
    "certifiedAt" TIMESTAMP(3) NOT NULL,
    "certifiedById" UUID NOT NULL,
    "pensionInfo" JSONB,
    "employmentInfo" JSONB,
    "personalInfo" JSONB,
    "indexationStartOfYear" JSONB,
    "pensionCorrectionsStartOfYear" JSONB,
    "voluntaryAdditionalContributions" JSONB,
    "pensionParameters" JSONB,
    "notes" TEXT,

    CONSTRAINT "CertifiedData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Role" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "name" TEXT NOT NULL,
    "description" TEXT,

    CONSTRAINT "Role_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "User_firebaseUid_key" ON "User"("firebaseUid");

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- CreateIndex
CREATE UNIQUE INDEX "PensionParameters_pensionDataId_key" ON "PensionParameters"("pensionDataId");

-- CreateIndex
CREATE INDEX "ChangeData_changeProposalId_idx" ON "ChangeData"("changeProposalId");

-- CreateIndex
CREATE UNIQUE INDEX "PersonalInfo_participantId_key" ON "PersonalInfo"("participantId");

-- CreateIndex
CREATE UNIQUE INDEX "Address_personalInfoId_key" ON "Address"("personalInfoId");

-- CreateIndex
CREATE UNIQUE INDEX "PartnerInfo_personalInfoId_key" ON "PartnerInfo"("personalInfoId");

-- CreateIndex
CREATE INDEX "Child_personalInfoId_idx" ON "Child"("personalInfoId");

-- CreateIndex
CREATE INDEX "Document_participantId_idx" ON "Document"("participantId");

-- CreateIndex
CREATE UNIQUE INDEX "EmploymentInfo_participantId_key" ON "EmploymentInfo"("participantId");

-- CreateIndex
CREATE INDEX "SalaryEntry_employmentInfoId_idx" ON "SalaryEntry"("employmentInfoId");

-- CreateIndex
CREATE UNIQUE INDEX "SalaryEntry_employmentInfoId_year_key" ON "SalaryEntry"("employmentInfoId", "year");

-- CreateIndex
CREATE UNIQUE INDEX "PensionInfo_participantId_key" ON "PensionInfo"("participantId");

-- CreateIndex
CREATE UNIQUE INDEX "PensionData_participantId_key" ON "PensionData"("participantId");

-- CreateIndex
CREATE UNIQUE INDEX "AnnualAccrual_pensionDataId_key" ON "AnnualAccrual"("pensionDataId");

-- CreateIndex
CREATE INDEX "VoluntaryContribution_pensionDataId_idx" ON "VoluntaryContribution"("pensionDataId");

-- CreateIndex
CREATE UNIQUE INDEX "ConversionDetails_pensionDataId_key" ON "ConversionDetails"("pensionDataId");

-- CreateIndex
CREATE INDEX "AuditLog_timestamp_idx" ON "AuditLog"("timestamp");

-- CreateIndex
CREATE INDEX "AuditLog_userId_idx" ON "AuditLog"("userId");

-- CreateIndex
CREATE INDEX "AuditLog_entityId_entityType_idx" ON "AuditLog"("entityId", "entityType");

-- CreateIndex
CREATE INDEX "AuditLog_proposalId_idx" ON "AuditLog"("proposalId");

-- CreateIndex
CREATE INDEX "CertifiedData_participantId_idx" ON "CertifiedData"("participantId");

-- CreateIndex
CREATE INDEX "CertifiedData_certificationYear_idx" ON "CertifiedData"("certificationYear");

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedData_participantId_certificationYear_key" ON "CertifiedData"("participantId", "certificationYear");

-- CreateIndex
CREATE UNIQUE INDEX "Role_name_key" ON "Role"("name");

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "Role"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PensionCorrections" ADD CONSTRAINT "PensionCorrections_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PensionCorrections" ADD CONSTRAINT "PensionCorrections_reviewedById_fkey" FOREIGN KEY ("reviewedById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SystemSettings" ADD CONSTRAINT "SystemSettings_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PensionParameters" ADD CONSTRAINT "PensionParameters_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChangeProposal" ADD CONSTRAINT "ChangeProposal_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChangeProposal" ADD CONSTRAINT "ChangeProposal_reviewedById_fkey" FOREIGN KEY ("reviewedById") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChangeProposal" ADD CONSTRAINT "ChangeProposal_entityId_fkey" FOREIGN KEY ("entityId") REFERENCES "Participant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChangeData" ADD CONSTRAINT "ChangeData_changeProposalId_fkey" FOREIGN KEY ("changeProposalId") REFERENCES "ChangeProposal"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PersonalInfo" ADD CONSTRAINT "PersonalInfo_participantId_fkey" FOREIGN KEY ("participantId") REFERENCES "Participant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Address" ADD CONSTRAINT "Address_personalInfoId_fkey" FOREIGN KEY ("personalInfoId") REFERENCES "PersonalInfo"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PartnerInfo" ADD CONSTRAINT "PartnerInfo_personalInfoId_fkey" FOREIGN KEY ("personalInfoId") REFERENCES "PersonalInfo"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Child" ADD CONSTRAINT "Child_personalInfoId_fkey" FOREIGN KEY ("personalInfoId") REFERENCES "PersonalInfo"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_participantId_fkey" FOREIGN KEY ("participantId") REFERENCES "Participant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EmploymentInfo" ADD CONSTRAINT "EmploymentInfo_participantId_fkey" FOREIGN KEY ("participantId") REFERENCES "Participant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SalaryEntry" ADD CONSTRAINT "SalaryEntry_employmentInfoId_fkey" FOREIGN KEY ("employmentInfoId") REFERENCES "EmploymentInfo"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PensionInfo" ADD CONSTRAINT "PensionInfo_participantId_fkey" FOREIGN KEY ("participantId") REFERENCES "Participant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PensionData" ADD CONSTRAINT "PensionData_participantId_fkey" FOREIGN KEY ("participantId") REFERENCES "Participant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PensionData" ADD CONSTRAINT "PensionData_id_fkey" FOREIGN KEY ("id") REFERENCES "PensionParameters"("pensionDataId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AnnualAccrual" ADD CONSTRAINT "AnnualAccrual_pensionDataId_fkey" FOREIGN KEY ("pensionDataId") REFERENCES "PensionData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "VoluntaryContribution" ADD CONSTRAINT "VoluntaryContribution_pensionDataId_fkey" FOREIGN KEY ("pensionDataId") REFERENCES "PensionData"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ConversionDetails" ADD CONSTRAINT "ConversionDetails_pensionDataId_fkey" FOREIGN KEY ("pensionDataId") REFERENCES "PensionData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AuditLog" ADD CONSTRAINT "AuditLog_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertifiedData" ADD CONSTRAINT "CertifiedData_participantId_fkey" FOREIGN KEY ("participantId") REFERENCES "Participant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertifiedData" ADD CONSTRAINT "CertifiedData_certifiedById_fkey" FOREIGN KEY ("certifiedById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
