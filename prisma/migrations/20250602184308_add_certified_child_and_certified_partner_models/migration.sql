-- AlterTable
ALTER TABLE "CertificationRejectReason" ADD COLUMN     "certifiedPartnerInfoId" UUID;

-- CreateTable
CREATE TABLE "CertifiedPartnerInfo" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedPersonalInfoId" UUID NOT NULL,
    "isCurrent" BOOLEAN NOT NULL,
    "firstName" TEXT,
    "lastName" TEXT,
    "dateOfBirth" TIMESTAMP(3),
    "isDeceased" BOOLEAN NOT NULL DEFAULT false,
    "startDate" TIMESTAMP(3),
    "pendingChanges" TEXT[],
    "requestedChanges" TEXT[],
    "approvedChanges" TEXT[],

    CONSTRAINT "CertifiedPartnerInfo_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "CertifiedPartnerInfo_certifiedPersonalInfoId_idx" ON "CertifiedPartnerInfo"("certifiedPersonalInfoId");

-- AddForeignKey
ALTER TABLE "CertifiedPartnerInfo" ADD CONSTRAINT "CertifiedPartnerInfo_certifiedPersonalInfoId_fkey" FOREIGN KEY ("certifiedPersonalInfoId") REFERENCES "CertifiedPersonalInfo"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedPartnerInfoId_fkey" FOREIGN KEY ("certifiedPartnerInfoId") REFERENCES "CertifiedPartnerInfo"("id") ON DELETE SET NULL ON UPDATE CASCADE;
