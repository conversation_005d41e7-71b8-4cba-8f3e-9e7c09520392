/*
  Warnings:

  - You are about to drop the column `changes` on the `AuditLog` table. All the data in the column will be lost.
  - Added the required column `changeDataId` to the `AuditLog` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "AuditLog" DROP COLUMN "changes",
ADD COLUMN     "changeDataId" UUID NOT NULL;

-- AlterTable
ALTER TABLE "ChangeData" ALTER COLUMN "newValue" SET DATA TYPE TEXT,
ALTER COLUMN "oldValue" SET DATA TYPE TEXT;

-- AddForeignKey
ALTER TABLE "AuditLog" ADD CONSTRAINT "AuditLog_changeDataId_fkey" FOREIGN KEY ("changeDataId") REFERENCES "ChangeData"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
