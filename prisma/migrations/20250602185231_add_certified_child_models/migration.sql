-- AlterTable
ALTER TABLE "CertificationRejectReason" ADD COLUMN     "certifiedChildId" UUID;

-- CreateTable
CREATE TABLE "CertifiedChild" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedDataId" UUID NOT NULL,
    "certifiedPersonalInfoId" UUID,
    "firstName" TEXT,
    "lastName" TEXT,
    "dateOfBirth" TIMESTAMP(3),
    "isOrphan" BOOLEAN NOT NULL DEFAULT false,
    "isStudying" BOOLEAN NOT NULL DEFAULT false,
    "pendingChanges" TEXT[],
    "requestedChanges" TEXT[],
    "approvedChanges" TEXT[],

    CONSTRAINT "CertifiedChild_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedChild_certifiedDataId_key" ON "CertifiedChild"("certifiedDataId");

-- CreateIndex
CREATE INDEX "CertifiedChild_certifiedPersonalInfoId_idx" ON "CertifiedChild"("certifiedPersonalInfoId");

-- AddForeignKey
ALTER TABLE "CertifiedChild" ADD CONSTRAINT "CertifiedChild_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertifiedChild" ADD CONSTRAINT "CertifiedChild_certifiedPersonalInfoId_fkey" FOREIGN KEY ("certifiedPersonalInfoId") REFERENCES "CertifiedPersonalInfo"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedChildId_fkey" FOREIGN KEY ("certifiedChildId") REFERENCES "CertifiedChild"("id") ON DELETE SET NULL ON UPDATE CASCADE;
