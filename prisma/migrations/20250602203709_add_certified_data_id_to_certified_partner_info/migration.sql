/*
  Warnings:

  - A unique constraint covering the columns `[certifiedDataId]` on the table `CertifiedPartnerInfo` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `certifiedDataId` to the `CertifiedPartnerInfo` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "CertifiedData" DROP CONSTRAINT "CertifiedData_certifiedPartnerInfoId_fkey";

-- AlterTable
ALTER TABLE "CertifiedPartnerInfo" ADD COLUMN     "certifiedDataId" UUID NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedPartnerInfo_certifiedDataId_key" ON "CertifiedPartnerInfo"("certifiedDataId");

-- AddForeignKey
ALTER TABLE "CertifiedPartnerInfo" ADD CONSTRAINT "CertifiedPartnerInfo_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE CASCADE ON UPDATE CASCADE;
