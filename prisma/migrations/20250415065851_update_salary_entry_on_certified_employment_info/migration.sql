/*
  Warnings:

  - You are about to drop the column `salaryEntries` on the `CertifiedEmploymentInfo` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "CertifiedEmploymentInfo" DROP COLUMN "salaryEntries";

-- CreateTable
CREATE TABLE "CertifiedSalaryEntry" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedEmploymentInfoId" UUID NOT NULL,
    "year" INTEGER NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "partTimePercentage" DOUBLE PRECISION NOT NULL,
    "pendingChanges" TEXT[],

    CONSTRAINT "CertifiedSalaryEntry_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "CertifiedSalaryEntry_certifiedEmploymentInfoId_idx" ON "CertifiedSalaryEntry"("certifiedEmploymentInfoId");

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedSalaryEntry_certifiedEmploymentInfoId_year_key" ON "CertifiedSalaryEntry"("certifiedEmploymentInfoId", "year");

-- AddForeignKey
ALTER TABLE "CertifiedSalaryEntry" ADD CONSTRAINT "CertifiedSalaryEntry_certifiedEmploymentInfoId_fkey" FOREIGN KEY ("certifiedEmploymentInfoId") REFERENCES "CertifiedEmploymentInfo"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
