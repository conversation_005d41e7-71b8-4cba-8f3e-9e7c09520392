-- AlterTable
ALTER TABLE "CertificationRejectReason" ADD COLUMN     "certifiedPartnerInfoId" UUID;

-- CreateTable
CREATE TABLE "CertifiedPartnerInfo" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedDataId" UUID NOT NULL,
    "firstName" TEXT,
    "lastName" TEXT,
    "dateOfBirth" TIMESTAMP(3),
    "isDeceased" BOOLEAN NOT NULL DEFAULT false,
    "startDate" TIMESTAMP(3),
    "isCurrent" BOOLEAN NOT NULL,
    "pendingChanges" TEXT[],
    "requestedChanges" TEXT[],
    "approvedChanges" TEXT[],

    CONSTRAINT "CertifiedPartnerInfo_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedPartnerInfo_certifiedDataId_key" ON "CertifiedPartnerInfo"("certifiedDataId");

-- AddForeignKey
ALTER TABLE "CertifiedPartnerInfo" ADD CONSTRAINT "CertifiedPartnerInfo_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedPartnerInfoId_fkey" FOREIGN KEY ("certifiedPartnerInfoId") REFERENCES "CertifiedPartnerInfo"("id") ON DELETE SET NULL ON UPDATE CASCADE;
