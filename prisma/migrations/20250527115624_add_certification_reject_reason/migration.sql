-- CreateTable
CREATE TABLE "CertificationRejectReason" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "field" TEXT NOT NULL,
    "reason" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "certifiedDataId" UUID,
    "certifiedPensionInfoId" UUID,
    "certifiedSalaryEntryId" UUID,
    "certifiedEmploymentInfoId" UUID,
    "certifiedPersonalInfoId" UUID,
    "certifiedIndexationStartOfYearId" UUID,
    "certifiedPensionCorrectionsId" UUID,
    "certifiedVoluntaryContributionsId" UUID,
    "certifiedPensionParametersId" UUID,

    CONSTRAINT "CertificationRejectReason_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedPensionInfoId_fkey" FOREIGN KEY ("certifiedPensionInfoId") REFERENCES "CertifiedPensionInfo"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedSalaryEntryId_fkey" FOREIGN KEY ("certifiedSalaryEntryId") REFERENCES "CertifiedSalaryEntry"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedEmploymentInfoId_fkey" FOREIGN KEY ("certifiedEmploymentInfoId") REFERENCES "CertifiedEmploymentInfo"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedPersonalInfoId_fkey" FOREIGN KEY ("certifiedPersonalInfoId") REFERENCES "CertifiedPersonalInfo"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedIndexationStartOfYearId_fkey" FOREIGN KEY ("certifiedIndexationStartOfYearId") REFERENCES "CertifiedIndexationStartOfYear"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedPensionCorrectionsId_fkey" FOREIGN KEY ("certifiedPensionCorrectionsId") REFERENCES "CertifiedPensionCorrections"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedVoluntaryContributionsI_fkey" FOREIGN KEY ("certifiedVoluntaryContributionsId") REFERENCES "CertifiedVoluntaryContributions"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedPensionParametersId_fkey" FOREIGN KEY ("certifiedPensionParametersId") REFERENCES "CertifiedPensionParameters"("id") ON DELETE SET NULL ON UPDATE CASCADE;
