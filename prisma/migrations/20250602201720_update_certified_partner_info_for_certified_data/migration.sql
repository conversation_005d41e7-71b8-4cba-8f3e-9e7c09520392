/*
  Warnings:

  - You are about to drop the column `certifiedPersonalInfoId` on the `CertifiedChild` table. All the data in the column will be lost.
  - You are about to drop the column `certifiedPersonalInfoId` on the `CertifiedPartnerInfo` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "CertifiedChild" DROP CONSTRAINT "CertifiedChild_certifiedPersonalInfoId_fkey";

-- DropForeignKey
ALTER TABLE "CertifiedPartnerInfo" DROP CONSTRAINT "CertifiedPartnerInfo_certifiedPersonalInfoId_fkey";

-- DropIndex
DROP INDEX "CertifiedChild_certifiedPersonalInfoId_idx";

-- DropIndex
DROP INDEX "CertifiedPartnerInfo_certifiedPersonalInfoId_idx";

-- AlterTable
ALTER TABLE "CertifiedChild" DROP COLUMN "certifiedPersonalInfoId";

-- AlterTable
ALTER TABLE "CertifiedData" ADD COLUMN     "certifiedPartnerInfoId" UUID;

-- AlterTable
ALTER TABLE "CertifiedPartnerInfo" DROP COLUMN "certifiedPersonalInfoId";

-- AddForeignKey
ALTER TABLE "CertifiedData" ADD CONSTRAINT "CertifiedData_certifiedPartnerInfoId_fkey" FOREIGN KEY ("certifiedPartnerInfoId") REFERENCES "CertifiedPartnerInfo"("id") ON DELETE SET NULL ON UPDATE CASCADE;
