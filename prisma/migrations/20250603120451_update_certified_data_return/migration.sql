-- AlterTable
ALTER TABLE "CertificationRejectReason" ADD COLUMN     "certifiedAddressId" UUID;

-- CreateTable
CREATE TABLE "CertifiedAddress" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedDataId" UUID NOT NULL,
    "street" TEXT,
    "houseNumber" TEXT,
    "postalCode" TEXT,
    "city" TEXT,
    "state" TEXT,
    "country" TEXT,
    "pendingChanges" TEXT[],
    "requestedChanges" TEXT[],
    "approvedChanges" TEXT[],

    CONSTRAINT "CertifiedAddress_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedAddress_certifiedDataId_key" ON "CertifiedAddress"("certifiedDataId");

-- AddForeignKey
ALTER TABLE "CertifiedAddress" ADD CONSTRAINT "CertifiedAddress_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedAddressId_fkey" FOREIGN KEY ("certifiedAddressId") REFERENCES "CertifiedAddress"("id") ON DELETE SET NULL ON UPDATE CASCADE;
