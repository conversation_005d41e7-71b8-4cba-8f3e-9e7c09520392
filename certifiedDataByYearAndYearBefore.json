{"2023": [{"id": "c460fc44-3f07-4296-9473-bcbb11197344", "participantId": "a28a5213-394b-48ab-88ed-5c50bbda37c7", "certificationYear": 2023, "certificationStatus": "completed", "certifiedAt": "2025-04-15T07:06:59.000Z", "certifiedById": "be196c0f-ee7b-42a3-9163-885f649e65ef", "notes": "asdfasdf", "participant": {"id": "a28a5213-394b-48ab-88ed-5c50bbda37c7", "createdAt": "2025-03-24T12:39:16.087Z", "createdBy": "be196c0f-ee7b-42a3-9163-885f649e65ef", "status": "Active", "updatedAt": "2025-03-24T15:39:06.000Z", "updatedBy": "be196c0f-ee7b-42a3-9163-885f649e65ef", "lastModified": "2025-03-24T15:39:10.000Z"}, "certifiedBy": {"id": "be196c0f-ee7b-42a3-9163-885f649e65ef", "firebaseUid": "bwzgkNU2SSSCA1HzoKJVJdI7mqL2", "createdAt": "2025-03-10T09:52:35.000Z", "updatedAt": "2025-03-10T09:52:38.000Z", "email": "<EMAIL>", "firstname": "Admin", "lastname": "Account", "lastLogin": "2025-03-10T09:53:07.000Z", "roleId": "8e56460b-f1ff-4c4d-b9da-a86858b8fe3f"}, "certifiedPensionInfo": {"id": "bbbce2f6-b469-487b-bca3-8d7cc2ef0a60", "certifiedDataId": "c460fc44-3f07-4296-9473-bcbb11197344", "code": 3, "codeDescription": "asdf as", "accruedGrossAnnualOldAgePension": 45, "accruedGrossAnnualPartnersPension": 45, "accruedGrossAnnualSinglesPension": 45, "attainableGrossAnnualOldAgePension": 45, "extraAccruedGrossAnnualOldAgePension": 45, "extraAccruedGrossAnnualPartnersPension": 45, "grossAnnualDisabilityPension": 33, "pensionBase": 56890, "pendingChanges": [], "requestedChanges": [], "approvedChanges": [], "certificationRejectReason": []}, "certifiedEmploymentInfo": {"id": "e21afa0b-f0dd-4302-b628-21014e6c5db0", "certifiedDataId": "c460fc44-3f07-4296-9473-bcbb11197344", "employeeId": "234234", "department": "HR", "position": "Admin", "regNum": 34, "havNum": 34, "startDate": "2007-05-01T18:33:55.000Z", "status": "Active", "pendingChanges": [], "requestedChanges": [], "approvedChanges": [], "certifiedSalaryEntries": [{"id": "028a104b-6872-4f5e-a18f-36e7692c1a9a", "certifiedEmploymentInfoId": "e21afa0b-f0dd-4302-b628-21014e6c5db0", "year": 2024, "amount": 2890, "partTimePercentage": 0.5, "pendingChanges": [], "requestedChanges": [], "approvedChanges": [], "certificationRejectReason": []}], "certificationRejectReason": []}, "certifiedPersonalInfo": {"id": "4b0a84c8-f80d-4849-99fd-eb9eb441638a", "certifiedDataId": "c460fc44-3f07-4296-9473-bcbb11197344", "firstName": "Junioasdf", "lastName": "asdfasdf", "email": "<EMAIL>", "phone": "039384534", "maritalStatus": "Married", "birthDay": 9, "birthMonth": 9, "birthYear": 2025, "address": {}, "partnerInfo": {}, "children": {}, "pendingChanges": [], "requestedChanges": [], "approvedChanges": [], "certificationRejectReason": []}, "certifiedIndexationStartOfYear": {"id": "81f67b9f-14dd-422a-9599-c2ff3181e38d", "certifiedDataId": "c460fc44-3f07-4296-9473-bcbb11197344", "accruedGrossAnnualOldAgePension": 77, "accruedGrossAnnualPartnersPension": 66.16, "accruedGrossAnnualSinglesPension": 4, "extraAccruedGrossAnnualOldAgePension": 4, "extraAccruedGrossAnnualPartnersPension": 45, "grossAnnualDisabilityPension": 4545, "pendingChanges": [], "requestedChanges": [], "approvedChanges": [], "certificationRejectReason": []}, "certifiedPensionCorrections": {"id": "cd545c32-4f8d-4dea-8b45-eb7d77af779f", "certifiedDataId": "c460fc44-3f07-4296-9473-bcbb11197344", "accruedGrossAnnualOldAgePension": 4, "attainableGrossAnnualOldAgePension": 1, "accruedGrossAnnualPartnersPension": 4, "accruedGrossAnnualSinglesPension": 4, "grossAnnualDisabilityPension": 1, "extraAccruedGrossAnnualOldAgePension": 34, "extraAccruedGrossAnnualPartnersPension": 34, "correction": 1, "year": "2024", "pendingChanges": [], "requestedChanges": [], "approvedChanges": [], "certificationRejectReason": []}, "certifiedVoluntaryContributions": {"id": "2990f4ce-fb04-4541-9a17-9d0b186e5abf", "certifiedDataId": "c460fc44-3f07-4296-9473-bcbb11197344", "contributions": 32, "pendingChanges": [], "requestedChanges": [], "approvedChanges": [], "certificationRejectReason": []}, "certifiedAddress": null, "certifiedChildren": [{"id": "fb0facf1-579f-48a9-8c3f-3e39d36b44ac", "certifiedDataId": "c460fc44-3f07-4296-9473-bcbb11197344", "firstName": "asdfasdfa asdf", "lastName": "asdfasdf", "dateOfBirth": "2029-06-03T12:31:30.000Z", "isOrphan": false, "isStudying": false, "pendingChanges": [], "requestedChanges": [], "approvedChanges": [], "certificationRejectReason": []}], "certifiedPartnerInfo": [{"id": "7f89280d-6fc2-453f-9747-c46d99c68c80", "certifiedDataId": "c460fc44-3f07-4296-9473-bcbb11197344", "firstName": "sdasd asdfa", "lastName": "asdfasdf", "dateOfBirth": "2015-06-03T12:30:43.000Z", "isDeceased": false, "startDate": "2034-06-03T12:30:59.000Z", "isCurrent": true, "pendingChanges": [], "requestedChanges": [], "approvedChanges": [], "certificationRejectReason": []}, {"id": "9ec36c64-72f4-46d0-844b-ea2d44d37bdc", "certifiedDataId": "c460fc44-3f07-4296-9473-bcbb11197344", "firstName": "sdasd asdfa", "lastName": "asdfasdf", "dateOfBirth": "2015-06-03T12:30:43.000Z", "isDeceased": false, "startDate": "2034-06-03T12:30:59.000Z", "isCurrent": false, "pendingChanges": [], "requestedChanges": [], "approvedChanges": [], "certificationRejectReason": []}], "certifiedPensionParameters": {"id": "d21dc0af-b608-4663-b722-329ea0b8f9cd", "certifiedDataId": "c460fc44-3f07-4296-9473-bcbb11197344", "accrualPercentage": 0.02, "annualMultiplier": 13, "offsetAmount": 17616, "partnersPensionPercentage": 0.7, "retirementAge": 65, "voluntaryContributionInterestRate": 0.03, "year": "2023", "effectiveDate": "2024-04-17T09:48:20.000Z", "pendingChanges": [], "requestedChanges": [], "approvedChanges": [], "certificationRejectReason": []}, "certificationRejectReason": []}], "2024": [{"id": "********-8723-464e-97db-23de1d13c7d2", "participantId": "a28a5213-394b-48ab-88ed-5c50bbda37c7", "certificationYear": 2024, "certificationStatus": "started", "certifiedAt": "2025-04-15T07:06:59.000Z", "certifiedById": "be196c0f-ee7b-42a3-9163-885f649e65ef", "notes": "asdfasdf", "participant": {"id": "a28a5213-394b-48ab-88ed-5c50bbda37c7", "createdAt": "2025-03-24T12:39:16.087Z", "createdBy": "be196c0f-ee7b-42a3-9163-885f649e65ef", "status": "Active", "updatedAt": "2025-03-24T15:39:06.000Z", "updatedBy": "be196c0f-ee7b-42a3-9163-885f649e65ef", "lastModified": "2025-03-24T15:39:10.000Z"}, "certifiedBy": {"id": "be196c0f-ee7b-42a3-9163-885f649e65ef", "firebaseUid": "bwzgkNU2SSSCA1HzoKJVJdI7mqL2", "createdAt": "2025-03-10T09:52:35.000Z", "updatedAt": "2025-03-10T09:52:38.000Z", "email": "<EMAIL>", "firstname": "Admin", "lastname": "Account", "lastLogin": "2025-03-10T09:53:07.000Z", "roleId": "8e56460b-f1ff-4c4d-b9da-a86858b8fe3f"}, "certifiedPensionInfo": {"id": "fd346c8f-78c7-4922-a8aa-6305c89a3136", "certifiedDataId": "********-8723-464e-97db-23de1d13c7d2", "code": 3, "codeDescription": "asdf as", "accruedGrossAnnualOldAgePension": 45, "accruedGrossAnnualPartnersPension": 45, "accruedGrossAnnualSinglesPension": 45, "attainableGrossAnnualOldAgePension": 45, "extraAccruedGrossAnnualOldAgePension": 45, "extraAccruedGrossAnnualPartnersPension": 45, "grossAnnualDisabilityPension": 33, "pensionBase": 56890, "pendingChanges": [], "requestedChanges": [], "approvedChanges": [], "certificationRejectReason": [], "differences": []}, "certifiedEmploymentInfo": {"id": "78963f2f-facb-4616-bec0-35ef1fa4f67b", "certifiedDataId": "********-8723-464e-97db-23de1d13c7d2", "employeeId": "234234", "department": "HR", "position": "Admin", "regNum": 34, "havNum": 34, "startDate": "2007-05-01T18:33:55.000Z", "status": "Active", "pendingChanges": [], "requestedChanges": [], "approvedChanges": [], "certifiedSalaryEntries": [{"id": "50231f36-1124-4e78-9ff5-b1998f8fe4e4", "certifiedEmploymentInfoId": "78963f2f-facb-4616-bec0-35ef1fa4f67b", "year": 2023, "amount": 345, "partTimePercentage": 0.4, "pendingChanges": [], "requestedChanges": [], "approvedChanges": [], "certificationRejectReason": []}], "certificationRejectReason": [], "differences": ["certifiedSalaryEntries"], "certifiedSalaryEntriesDifferences": []}, "certifiedPersonalInfo": {"id": "ca176df6-f999-414b-8969-0d14fc1913a0", "certifiedDataId": "********-8723-464e-97db-23de1d13c7d2", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "phone": "039384534", "maritalStatus": "Married", "birthDay": 9, "birthMonth": 9, "birthYear": 2025, "address": {}, "partnerInfo": {}, "children": {}, "pendingChanges": [], "requestedChanges": [], "approvedChanges": [], "certificationRejectReason": [], "differences": ["firstName", "lastName", "email"]}, "certifiedIndexationStartOfYear": {"id": "a4061a86-9086-4bda-a60f-a8719908557a", "certifiedDataId": "********-8723-464e-97db-23de1d13c7d2", "accruedGrossAnnualOldAgePension": 77, "accruedGrossAnnualPartnersPension": 66.16, "accruedGrossAnnualSinglesPension": 4, "extraAccruedGrossAnnualOldAgePension": 4, "extraAccruedGrossAnnualPartnersPension": 45, "grossAnnualDisabilityPension": 545, "pendingChanges": [], "requestedChanges": [], "approvedChanges": ["string"], "certificationRejectReason": [], "differences": ["grossAnnualDisabilityPension", "approvedChanges"]}, "certifiedPensionCorrections": {"id": "0cfdeea7-aa55-48de-a177-ccbbcaadf9b6", "certifiedDataId": "********-8723-464e-97db-23de1d13c7d2", "accruedGrossAnnualOldAgePension": 4, "attainableGrossAnnualOldAgePension": 1, "accruedGrossAnnualPartnersPension": 4, "accruedGrossAnnualSinglesPension": 4, "grossAnnualDisabilityPension": 1, "extraAccruedGrossAnnualOldAgePension": 34, "extraAccruedGrossAnnualPartnersPension": 33, "correction": 1, "year": "2024", "pendingChanges": [], "requestedChanges": [], "approvedChanges": [], "certificationRejectReason": [], "differences": ["extraAccruedGrossAnnualPartnersPension"]}, "certifiedVoluntaryContributions": {"id": "0c6b3667-6d9a-4ee9-89a0-40a80381676e", "certifiedDataId": "********-8723-464e-97db-23de1d13c7d2", "contributions": 37, "pendingChanges": [], "requestedChanges": [], "approvedChanges": [], "certificationRejectReason": [], "differences": ["contributions"]}, "certifiedAddress": {"id": "f5e0c4ac-2a7d-4924-9fdc-6a7c68aa3bc3", "certifiedDataId": "********-8723-464e-97db-23de1d13c7d2", "street": "asdfasd", "houseNumber": "23", "postalCode": "23323", "city": "zvza", "state": "asdfas", "country": "<PERSON><PERSON><PERSON>", "pendingChanges": [], "requestedChanges": [], "approvedChanges": [], "certificationRejectReason": [], "differences": ["_exists"]}, "certifiedChildren": [{"id": "e67cc7be-e74c-47da-9fa2-fdcbe434175d", "certifiedDataId": "********-8723-464e-97db-23de1d13c7d2", "firstName": "asfasdf", "lastName": "dasdf", "dateOfBirth": "2015-06-02T22:02:03.000Z", "isOrphan": false, "isStudying": true, "pendingChanges": [], "requestedChanges": [], "approvedChanges": [], "certificationRejectReason": []}], "certifiedPartnerInfo": [{"id": "198b289c-7818-49c4-9c64-5b81348c8bdc", "certifiedDataId": "********-8723-464e-97db-23de1d13c7d2", "firstName": "asdf asd", "lastName": "asdfasdf", "dateOfBirth": "2017-06-03T11:31:15.000Z", "isDeceased": false, "startDate": "2025-06-03T11:31:28.000Z", "isCurrent": true, "pendingChanges": [], "requestedChanges": [], "approvedChanges": [], "certificationRejectReason": []}, {"id": "63f9260e-0862-4ac4-9355-860910c12f7b", "certifiedDataId": "********-8723-464e-97db-23de1d13c7d2", "firstName": "asdf asd", "lastName": "asdfasdf", "dateOfBirth": "2017-06-03T11:31:15.000Z", "isDeceased": false, "startDate": "2025-06-03T11:31:28.000Z", "isCurrent": false, "pendingChanges": [], "requestedChanges": [], "approvedChanges": [], "certificationRejectReason": []}, {"id": "aa7ae6f5-3b89-4424-b61d-e84a7200e97e", "certifiedDataId": "********-8723-464e-97db-23de1d13c7d2", "firstName": "asdf asd", "lastName": "asdfasdf", "dateOfBirth": "2017-06-03T11:31:15.000Z", "isDeceased": false, "startDate": "2025-06-03T11:31:28.000Z", "isCurrent": false, "pendingChanges": [], "requestedChanges": [], "approvedChanges": [], "certificationRejectReason": []}, {"id": "4b2c4981-253c-413a-9865-c0ca8f1b580b", "certifiedDataId": "********-8723-464e-97db-23de1d13c7d2", "firstName": "asdf asd", "lastName": "asdfasdf", "dateOfBirth": "2017-06-03T11:31:15.000Z", "isDeceased": false, "startDate": "2025-06-03T11:31:28.000Z", "isCurrent": false, "pendingChanges": [], "requestedChanges": [], "approvedChanges": [], "certificationRejectReason": []}], "certifiedPensionParameters": {"id": "5b072186-6509-44e4-b93c-524f4d7fa540", "certifiedDataId": "********-8723-464e-97db-23de1d13c7d2", "accrualPercentage": 0.02, "annualMultiplier": 13, "offsetAmount": 17616, "partnersPensionPercentage": 0.7, "retirementAge": 65, "voluntaryContributionInterestRate": 0.04, "year": "2024", "effectiveDate": "2024-04-17T09:48:20.000Z", "pendingChanges": [], "requestedChanges": [], "approvedChanges": [], "certificationRejectReason": [], "differences": ["voluntaryContributionInterestRate", "year"]}, "certificationRejectReason": [], "differences": ["certificationStatus"], "certifiedChildrenDifferences": ["[0].first<PERSON><PERSON>", "[0].last<PERSON><PERSON>", "[0].dateOfBirth", "[0].isStudying"], "certifiedPartnerInfoDifferences": ["_count"]}]}