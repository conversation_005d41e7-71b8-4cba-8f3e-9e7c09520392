# Certified Data Models API Documentation

This document describes the GraphQL operations available for the certified data models in the Pension Administration API.

## Overview

The certified data models store snapshots of important pension-related data for certified years. Each certified data record can have related information stored in the following models:

- CertifiedPensionInfo
- CertifiedEmploymentInfo
- CertifiedPersonalInfo
- CertifiedIndexationStartOfYear
- CertifiedPensionCorrections
- CertifiedVoluntaryContributions
- CertifiedPensionParameters

Each model supports querying by ID, querying by CertifiedData ID, and creation operations.

## GraphQL Operations

### CertifiedPensionInfo

#### Queries

```graphql
# Get certified pension info by ID
query GetCertifiedPensionInfo($id: String!) {
  certifiedPensionInfo(id: $id) {
    id
    certifiedDataId
    code
    codeDescription
    certifiedData {
      id
      certificationYear
    }
  }
}

# Get certified pension info by certified data ID
query GetCertifiedPensionInfoByCertifiedDataId($certifiedDataId: String!) {
  certifiedPensionInfoByCertifiedDataId(certifiedDataId: $certifiedDataId) {
    id
    certifiedDataId
    code
    codeDescription
    certifiedData {
      id
      certificationYear
    }
  }
}
```

#### Mutations

```graphql
# Create certified pension info
mutation CreateCertifiedPensionInfo($input: CreateCertifiedPensionInfoInput!, $certifiedDataId: String!) {
  createCertifiedPensionInfo(createCertifiedPensionInfoInput: $input, certifiedDataId: $certifiedDataId) {
    id
    certifiedDataId
    code
    codeDescription
  }
}
```

### CertifiedEmploymentInfo

#### Queries

```graphql
# Get certified employment info by ID
query GetCertifiedEmploymentInfo($id: String!) {
  certifiedEmploymentInfo(id: $id) {
    id
    certifiedDataId
    employeeId
    department
    position
    regNum
    havNum
    startDate
    status
    salaryEntries
    certifiedData {
      id
      certificationYear
    }
  }
}

# Get certified employment info by certified data ID
query GetCertifiedEmploymentInfoByCertifiedDataId($certifiedDataId: String!) {
  certifiedEmploymentInfoByCertifiedDataId(certifiedDataId: $certifiedDataId) {
    id
    certifiedDataId
    employeeId
    department
    position
    regNum
    havNum
    startDate
    status
    salaryEntries
    certifiedData {
      id
      certificationYear
    }
  }
}
```

#### Mutations

```graphql
# Create certified employment info
mutation CreateCertifiedEmploymentInfo($input: CreateCertifiedEmploymentInfoInput!, $certifiedDataId: String!) {
  createCertifiedEmploymentInfo(createCertifiedEmploymentInfoInput: $input, certifiedDataId: $certifiedDataId) {
    id
    certifiedDataId
    employeeId
    department
    position
    regNum
    havNum
    startDate
    status
    salaryEntries
  }
}
```

### CertifiedPersonalInfo

#### Queries

```graphql
# Get certified personal info by ID
query GetCertifiedPersonalInfo($id: String!) {
  certifiedPersonalInfo(id: $id) {
    id
    certifiedDataId
    firstName
    lastName
    email
    phone
    maritalStatus
    birthDay
    birthMonth
    birthYear
    address
    partnerInfo
    children
    certifiedData {
      id
      certificationYear
    }
  }
}

# Get certified personal info by certified data ID
query GetCertifiedPersonalInfoByCertifiedDataId($certifiedDataId: String!) {
  certifiedPersonalInfoByCertifiedDataId(certifiedDataId: $certifiedDataId) {
    id
    certifiedDataId
    firstName
    lastName
    email
    phone
    maritalStatus
    birthDay
    birthMonth
    birthYear
    address
    partnerInfo
    children
    certifiedData {
      id
      certificationYear
    }
  }
}
```

#### Mutations

```graphql
# Create certified personal info
mutation CreateCertifiedPersonalInfo($input: CreateCertifiedPersonalInfoInput!, $certifiedDataId: String!) {
  createCertifiedPersonalInfo(createCertifiedPersonalInfoInput: $input, certifiedDataId: $certifiedDataId) {
    id
    certifiedDataId
    firstName
    lastName
    email
    phone
    maritalStatus
    birthDay
    birthMonth
    birthYear
    address
    partnerInfo
    children
  }
}
```

### CertifiedIndexationStartOfYear

#### Queries

```graphql
# Get certified indexation by ID
query GetCertifiedIndexationStartOfYear($id: String!) {
  certifiedIndexationStartOfYear(id: $id) {
    id
    certifiedDataId
    accruedGrossAnnualOldAgePension
    accruedGrossAnnualPartnersPension
    accruedGrossAnnualSinglesPension
    extraAccruedGrossAnnualOldAgePension
    extraAccruedGrossAnnualPartnersPension
    grossAnnualDisabilityPension
    certifiedData {
      id
      certificationYear
    }
  }
}

# Get certified indexation by certified data ID
query GetCertifiedIndexationByCertifiedDataId($certifiedDataId: String!) {
  certifiedIndexationByCertifiedDataId(certifiedDataId: $certifiedDataId) {
    id
    certifiedDataId
    accruedGrossAnnualOldAgePension
    accruedGrossAnnualPartnersPension
    accruedGrossAnnualSinglesPension
    extraAccruedGrossAnnualOldAgePension
    extraAccruedGrossAnnualPartnersPension
    grossAnnualDisabilityPension
    certifiedData {
      id
      certificationYear
    }
  }
}
```

#### Mutations

```graphql
# Create certified indexation
mutation CreateCertifiedIndexationStartOfYear($input: CreateCertifiedIndexationStartOfYearInput!, $certifiedDataId: String!) {
  createCertifiedIndexationStartOfYear(createCertifiedIndexationInput: $input, certifiedDataId: $certifiedDataId) {
    id
    certifiedDataId
    accruedGrossAnnualOldAgePension
    accruedGrossAnnualPartnersPension
    accruedGrossAnnualSinglesPension
    extraAccruedGrossAnnualOldAgePension
    extraAccruedGrossAnnualPartnersPension
    grossAnnualDisabilityPension
  }
}
```

### CertifiedPensionCorrections

#### Queries

```graphql
# Get certified pension corrections by ID
query GetCertifiedPensionCorrections($id: String!) {
  certifiedPensionCorrections(id: $id) {
    id
    certifiedDataId
    accruedGrossAnnualOldAgePension
    attainableGrossAnnualOldAgePension
    accruedGrossAnnualPartnersPension
    accruedGrossAnnualSinglesPension
    grossAnnualDisabilityPension
    correction
    year
    certifiedData {
      id
      certificationYear
    }
  }
}

# Get certified pension corrections by certified data ID
query GetCertifiedCorrectionsByCertifiedDataId($certifiedDataId: String!) {
  certifiedCorrectionsByCertifiedDataId(certifiedDataId: $certifiedDataId) {
    id
    certifiedDataId
    accruedGrossAnnualOldAgePension
    attainableGrossAnnualOldAgePension
    accruedGrossAnnualPartnersPension
    accruedGrossAnnualSinglesPension
    grossAnnualDisabilityPension
    correction
    year
    certifiedData {
      id
      certificationYear
    }
  }
}
```

#### Mutations

```graphql
# Create certified pension corrections
mutation CreateCertifiedPensionCorrections($input: CreateCertifiedPensionCorrectionsInput!, $certifiedDataId: String!) {
  createCertifiedPensionCorrections(createCertifiedCorrectionsInput: $input, certifiedDataId: $certifiedDataId) {
    id
    certifiedDataId
    accruedGrossAnnualOldAgePension
    attainableGrossAnnualOldAgePension
    accruedGrossAnnualPartnersPension
    accruedGrossAnnualSinglesPension
    grossAnnualDisabilityPension
    correction
    year
  }
}
```

### CertifiedVoluntaryContributions

#### Queries

```graphql
# Get certified voluntary contributions by ID
query GetCertifiedVoluntaryContributions($id: String!) {
  certifiedVoluntaryContributions(id: $id) {
    id
    certifiedDataId
    contributions
    certifiedData {
      id
      certificationYear
    }
  }
}

# Get certified voluntary contributions by certified data ID
query GetCertifiedContributionsByCertifiedDataId($certifiedDataId: String!) {
  certifiedContributionsByCertifiedDataId(certifiedDataId: $certifiedDataId) {
    id
    certifiedDataId
    contributions
    certifiedData {
      id
      certificationYear
    }
  }
}
```

#### Mutations

```graphql
# Create certified voluntary contributions
mutation CreateCertifiedVoluntaryContributions($input: CreateCertifiedVoluntaryContributionsInput!, $certifiedDataId: String!) {
  createCertifiedVoluntaryContributions(createCertifiedContributionsInput: $input, certifiedDataId: $certifiedDataId) {
    id
    certifiedDataId
    contributions
  }
}
```

### CertifiedPensionParameters

#### Queries

```graphql
# Get certified pension parameters by ID
query GetCertifiedPensionParameters($id: String!) {
  certifiedPensionParameters(id: $id) {
    id
    certifiedDataId
    accrualPercentage
    annualMultiplier
    offsetAmount
    partnersPensionPercentage
    retirementAge
    voluntaryContributionInterestRate
    year
    effectiveDate
    certifiedData {
      id
      certificationYear
    }
  }
}

# Get certified pension parameters by certified data ID
query GetCertifiedParametersByCertifiedDataId($certifiedDataId: String!) {
  certifiedParametersByCertifiedDataId(certifiedDataId: $certifiedDataId) {
    id
    certifiedDataId
    accrualPercentage
    annualMultiplier
    offsetAmount
    partnersPensionPercentage
    retirementAge
    voluntaryContributionInterestRate
    year
    effectiveDate
    certifiedData {
      id
      certificationYear
    }
  }
}
```

#### Mutations

```graphql
# Create certified pension parameters
mutation CreateCertifiedPensionParameters($input: CreateCertifiedPensionParametersInput!, $certifiedDataId: String!) {
  createCertifiedPensionParameters(createCertifiedParametersInput: $input, certifiedDataId: $certifiedDataId) {
    id
    certifiedDataId
    accrualPercentage
    annualMultiplier
    offsetAmount
    partnersPensionPercentage
    retirementAge
    voluntaryContributionInterestRate
    year
    effectiveDate
  }
}
```

## Data Migration

For historical data, we provide a migration utility to convert existing JSON data in the CertifiedData model to these new relational models:

```bash
# Run the data migration
npm run migrate:certified-data

# After verifying the migration was successful, remove the legacy JSON fields
npm run migrate:certified-data:finalize
```

## Testing

To test these new models, run:

```bash
npm run test:certified-models
```

This will create a test certified data record with related records in all the new models. 